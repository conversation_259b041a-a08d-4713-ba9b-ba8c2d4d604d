import { createClient } from "@supabase/supabase-js";
import dotenv from "dotenv";

// Parse command line arguments
const args = process.argv.slice(2);
const helpArg = args.includes('--help') || args.includes('-h');

if (helpArg) {
  console.log(`
Usage: ts-node seed-case-files-quebec.ts [options]

Options:
  --env=<environment>  Specify which environment file to use:
                       'local' - Use .env.local (default)
                       'cloud' - Use .env
  --help, -h           Show this help message

Examples:
  ts-node seed-case-files-quebec.ts                 # Uses .env.local
  ts-node seed-case-files-quebec.ts --env=cloud     # Uses .env
  `);
  process.exit(0);
}

const envArg = args.find(arg => arg.startsWith('--env='));
const envFile = envArg ? envArg.split('=')[1] : 'local';

// Load environment variables from the appropriate file
const envPath = envFile === 'cloud' ? '.env' : '.env.local';
console.log(`Using environment file: ${envPath}`);
dotenv.config({ path: envPath });

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error("Missing environment variables");
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false,
  },
});

// Quebec family data
const quebecFamilies = [
  {
    name: "Tremblay",
    region: "Montréal",
    situation: "Séparation récente, visites supervisées requises",
    children_ages: [6, 9],
    contacts: [
      { name: "Marie Tremblay", email: "<EMAIL>", phone: "************", address: "123 Rue Saint-Denis, Montréal, QC H2X 3K8", role: "parent_gardien" },
      { name: "Pierre Tremblay", email: "<EMAIL>", phone: "************", address: "456 Avenue du Parc, Montréal, QC H2V 4E7", role: "parent_non_gardien" },
      { name: "Sophie Tremblay", email: "", phone: "", address: "123 Rue Saint-Denis, Montréal, QC H2X 3K8", role: "enfant" },
      { name: "Lucas Tremblay", email: "", phone: "", address: "123 Rue Saint-Denis, Montréal, QC H2X 3K8", role: "enfant" }
    ],
    case_status: "opening"
  },
  {
    name: "Bouchard",
    region: "Québec",
    situation: "Visites supervisées en cours, progrès positifs",
    children_ages: [4, 7, 12],
    contacts: [
      { name: "Julie Bouchard", email: "<EMAIL>", phone: "************", address: "789 Rue de la Couronne, Québec, QC G1R 3V4", role: "parent_gardien" },
      { name: "Marc Bouchard", email: "<EMAIL>", phone: "************", address: "321 Boulevard Charest, Québec, QC G1N 2E5", role: "parent_non_gardien" }
    ],
    case_status: "active"
  },
  {
    name: "Gagnon",
    region: "Sherbrooke",
    situation: "Suspension temporaire - non-respect des conditions",
    children_ages: [5, 8],
    contacts: [
      { name: "Sylvie Gagnon", email: "<EMAIL>", phone: "************", address: "654 Rue King Ouest, Sherbrooke, QC J1H 1S1", role: "parent_gardien" },
      { name: "Robert Gagnon", email: "<EMAIL>", phone: "************", address: "987 Rue Belvédère Sud, Sherbrooke, QC J1H 4C3", role: "parent_non_gardien" }
    ],
    case_status: "suspended"
  },
  {
    name: "Lavoie",
    region: "Trois-Rivières",
    situation: "Objectifs atteints - réunification familiale réussie",
    children_ages: [3, 6],
    contacts: [
      { name: "Nathalie Lavoie", email: "<EMAIL>", phone: "************", address: "147 Rue des Forges, Trois-Rivières, QC G9A 2G8", role: "parent_gardien" },
      { name: "Daniel Lavoie", email: "<EMAIL>", phone: "************", address: "258 Boulevard des Récollets, Trois-Rivières, QC G8Z 3X1", role: "parent_non_gardien" }
    ],
    case_status: "closed"
  },
  {
    name: "Roy",
    region: "Gatineau",
    situation: "Famille bilingue, services en français et anglais",
    children_ages: [10, 14],
    contacts: [
      { name: "Catherine Roy", email: "<EMAIL>", phone: "************", address: "369 Boulevard Maloney Est, Gatineau, QC J8P 1E6", role: "parent_gardien" },
      { name: "Michael Roy", email: "<EMAIL>", phone: "************", address: "741 Riverside Drive, Ottawa, ON K1S 3W2", role: "parent_non_gardien" }
    ],
    case_status: "opening"
  }
];

async function ensureOrganization() {
  console.log("Ensuring organization exists...");

  const { error } = await supabase
    .from("organizations")
    .select("id")
    .eq("id", "********-0000-0000-0000-********0001")
    .single();

  if (error && error.message.includes("No rows found")) {
    const { error: createError } = await supabase
      .from("organizations")
      .insert({
        id: "********-0000-0000-0000-********0001",
        name: "RQRSDA Montreal",
        status: "active",
      });

    if (createError) {
      console.error("Error creating organization:", createError);
      return null;
    }
    console.log("Created organization");
  }

  return "********-0000-0000-0000-********0001";
}

async function ensureUsers() {
  console.log("Ensuring users exist...");

  const users = [
    { email: "<EMAIL>", role: "Director", firstName: "Organization", lastName: "Director" },
    { email: "<EMAIL>", role: "Coordinator", firstName: "Visit", lastName: "Coordinator" },
    { email: "<EMAIL>", role: "SocialWorker", firstName: "Social", lastName: "Worker" }
  ];

  const createdUsers = [];

  for (const userData of users) {
    // Check if user exists
    const { data: existingUsers } = await supabase.auth.admin.listUsers();
    const existingUser = existingUsers.users.find(u => u.email === userData.email);

    if (existingUser) {
      console.log(`User ${userData.email} already exists`);
      createdUsers.push(existingUser);
      continue;
    }

    // Create user
    const { data: authUser, error: createError } = await supabase.auth.admin.createUser({
      email: userData.email,
      password: "Password123!",
      email_confirm: true,
      user_metadata: {
        first_name: userData.firstName,
        last_name: userData.lastName,
      },
    });

    if (createError) {
      console.error(`Error creating user ${userData.email}:`, createError);
      continue;
    }

    console.log(`Created user: ${userData.email}`);
    createdUsers.push(authUser.user);

    // Create user role
    const { error: roleError } = await supabase.from("user_roles").insert({
      user_id: authUser.user.id,
      role: userData.role,
      organization_id: "********-0000-0000-0000-********0001",
    });

    if (roleError) {
      console.error(`Error assigning role to ${userData.email}:`, roleError);
    }
  }

  return createdUsers;
}

async function ensureEmployees(users: any[]) {
  console.log("Ensuring employees exist...");

  const employeeData = [
    {
      firstName: "Social", lastName: "Worker", jobTitle: "Social Worker",
      email: "<EMAIL>", phone: "************"
    },
    {
      firstName: "Jean", lastName: "Dubois", jobTitle: "Case Manager",
      email: "<EMAIL>", phone: "************"
    },
    {
      firstName: "Visit", lastName: "Coordinator", jobTitle: "Program Coordinator",
      email: "<EMAIL>", phone: "************"
    }
  ];

  const createdEmployees = [];

  for (const empData of employeeData) {
    // Check if employee exists
    const { data: existingEmp } = await supabase
      .from("employees")
      .select("id")
      .eq("first_name", empData.firstName)
      .eq("last_name", empData.lastName)
      .single();

    if (existingEmp) {
      console.log(`Employee ${empData.firstName} ${empData.lastName} already exists`);
      createdEmployees.push(existingEmp);
      continue;
    }

    // Find matching user
    const matchingUser = users.find(u => u.email === empData.email);

    // Create employee
    const { data: employee, error: createError } = await supabase
      .from("employees")
      .insert({
        organization_id: "********-0000-0000-0000-********0001",
        user_account_id: matchingUser?.id || null,
        first_name: empData.firstName,
        last_name: empData.lastName,
        job_title: empData.jobTitle,
        employment_status: "active",
        emails: [{ email: empData.email, type: "Work", primary: true }],
        phones: [{ number: empData.phone, type: "Work", primary: true }]
      })
      .select()
      .single();

    if (createError) {
      console.error(`Error creating employee ${empData.firstName} ${empData.lastName}:`, createError);
      continue;
    }

    console.log(`Created employee: ${empData.firstName} ${empData.lastName}`);
    createdEmployees.push(employee);
  }

  return createdEmployees;
}

async function createContacts(organizationId: string) {
  console.log("Creating Quebec family contacts...");

  const createdContacts = [];

  for (const family of quebecFamilies) {
    for (const contact of family.contacts) {
      // Check if contact exists
      const { data: existingContact } = await supabase
        .from("contacts")
        .select("id")
        .eq("name", contact.name)
        .single();

      if (existingContact) {
        console.log(`Contact ${contact.name} already exists`);
        createdContacts.push(existingContact);
        continue;
      }

      // Create contact
      const { data: newContact, error: createError } = await supabase
        .from("contacts")
        .insert({
          organization_id: organizationId,
          name: contact.name,
          email: contact.email ? { personal: contact.email } : {},
          phone: contact.phone ? { mobile: contact.phone } : {},
          address: contact.address,
          status: "active"
        })
        .select()
        .single();

      if (createError) {
        console.error(`Error creating contact ${contact.name}:`, createError);
        continue;
      }

      console.log(`Created contact: ${contact.name}`);
      createdContacts.push(newContact);
    }
  }

  return createdContacts;
}

async function createRequests(organizationId: string, users: any[]) {
  console.log("Creating requests for Quebec families...");

  const createdRequests = [];
  const directorUser = users.find(u => u.email === "<EMAIL>");

  for (let i = 0; i < quebecFamilies.length; i++) {
    const family = quebecFamilies[i];

    // Check if request exists by checking request_metadata
    const { data: existingMetadata } = await supabase
      .from("request_metadata")
      .select("request_id")
      .ilike("service_requirements->>famille", `%${family.name}%`)
      .single();

    if (existingMetadata) {
      console.log(`Request for family ${family.name} already exists`);
      const { data: existingRequest } = await supabase
        .from("requests")
        .select("id")
        .eq("id", existingMetadata.request_id)
        .single();
      if (existingRequest) {
        createdRequests.push(existingRequest);
        continue;
      }
    }

    // Create request with proper schema
    const { data: newRequest, error: createError } = await supabase
      .from("requests")
      .insert({
        organization_id: organizationId,
        requester_id: directorUser?.id || users[0]?.id,
        status: "completed", // Ready for case file creation
        location_id: null, // Will be set when locations are available
        service_id: null, // Will be set when services are available
        start_date: new Date().toISOString().split('T')[0],
        end_date: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000).toISOString().split('T')[0], // 1 year from now
        duration: "120", // 2 hours
        periodicity: "weekly",
        frequency_count: "1"
      })
      .select()
      .single();

    if (createError) {
      console.error(`Error creating request for family ${family.name}:`, createError);
      continue;
    }

    // Create request metadata
    const { error: metadataError } = await supabase
      .from("request_metadata")
      .insert({
        request_id: newRequest.id,
        service_requirements: {
          famille: family.name,
          region: family.region,
          situation: family.situation,
          enfants_ages: family.children_ages,
          type_service: "Visites supervisées",
          priorite: family.case_status === "suspended" ? "elevee" : "normale",
          frequency: "weekly",
          duration: 120,
          specialRequirements: family.case_status === "suspended" ? ["supervision_renforcee"] : [],
          notes: family.situation
        },
        family_availability: {
          weekdayAvailability: {
            monday: { available: true, timeSlots: [{ start: "09:00", end: "17:00" }] },
            tuesday: { available: true, timeSlots: [{ start: "09:00", end: "17:00" }] },
            wednesday: { available: true, timeSlots: [{ start: "09:00", end: "17:00" }] },
            thursday: { available: true, timeSlots: [{ start: "09:00", end: "17:00" }] },
            friday: { available: true, timeSlots: [{ start: "09:00", end: "17:00" }] },
            saturday: { available: true, timeSlots: [{ start: "09:00", end: "15:00" }] },
            sunday: { available: false, timeSlots: [] }
          },
          notes: `Disponibilité pour famille ${family.name}`
        }
      });

    if (metadataError) {
      console.error(`Error creating metadata for family ${family.name}:`, metadataError);
    }

    console.log(`Created request for family: ${family.name}`);
    createdRequests.push(newRequest);
  }

  return createdRequests;
}

async function createCaseFiles(organizationId: string, users: any[], employees: any[], requests: any[], contacts: any[]) {
  console.log("Creating Quebec case files...");

  const createdCaseFiles = [];
  const directorUser = users.find(u => u.email === "<EMAIL>");

  for (let i = 0; i < quebecFamilies.length; i++) {
    const family = quebecFamilies[i];
    const request = requests[i];
    const employee = employees[i % employees.length]; // Rotate through employees

    if (!request) {
      console.warn(`No request found for family ${family.name}, skipping case file`);
      continue;
    }

    // Check if case file exists
    const { data: existingCaseFile } = await supabase
      .from("case_files")
      .select("id")
      .eq("request_id", request.id)
      .single();

    if (existingCaseFile) {
      console.log(`Case file for family ${family.name} already exists`);
      createdCaseFiles.push(existingCaseFile);
      continue;
    }

    // Create case file
    const caseNumber = `CF-${new Date().getFullYear()}-${String(i + 1).padStart(3, '0')}`;
    const now = new Date().toISOString();

    const { data: newCaseFile, error: createError } = await supabase
      .from("case_files")
      .insert({
        organization_id: organizationId,
        request_id: request.id,
        case_number: caseNumber,
        status: family.case_status,
        created_by: directorUser?.id || users[0]?.id,
        assigned_to: employee?.id,
        created_at: now,
        updated_at: now,
        opened_at: now,
        activated_at: family.case_status === "active" || family.case_status === "suspended" || family.case_status === "closed" ? now : null,
        suspended_at: family.case_status === "suspended" ? now : null,
        closed_at: family.case_status === "closed" ? now : null,
        metadata: {
          famille: family.name,
          situation: family.situation,
          enfants_ages: family.children_ages,
          langue_principale: "français",
          region: family.region,
          priorite: family.case_status === "suspended" ? "elevee" : "normale"
        }
      })
      .select()
      .single();

    if (createError) {
      console.error(`Error creating case file for family ${family.name}:`, createError);
      continue;
    }

    console.log(`Created case file: ${caseNumber} for family ${family.name}`);
    createdCaseFiles.push(newCaseFile);

    // Create case file contacts
    const familyContacts = contacts.filter(c =>
      family.contacts.some(fc => fc.name === c.name)
    );

    for (const contact of familyContacts) {
      const contactRole = family.contacts.find(fc => fc.name === contact.name)?.role || "contact";

      const { error: linkError } = await supabase
        .from("case_file_contacts")
        .insert({
          organization_id: organizationId,
          case_file_id: newCaseFile.id,
          contact_id: contact.id,
          relationship_type: contactRole
        });

      if (linkError) {
        console.error(`Error linking contact ${contact.name} to case file:`, linkError);
      }
    }

    // Create case file history entry
    const { error: historyError } = await supabase
      .from("case_file_history")
      .insert({
        organization_id: organizationId,
        case_file_id: newCaseFile.id,
        user_id: directorUser?.id || users[0]?.id,
        action: "created",
        changes: {
          status: { from: null, to: family.case_status },
          case_number: { from: null, to: caseNumber },
          note: `Dossier créé pour famille ${family.name} - ${family.situation}`
        }
      });

    if (historyError) {
      console.error(`Error creating history for case file ${caseNumber}:`, historyError);
    }
  }

  return createdCaseFiles;
}

async function seedCaseFilesQuebec() {
  try {
    console.log("Starting Quebec case files seeding...");

    // 1. Ensure organization exists
    const organizationId = await ensureOrganization();
    if (!organizationId) {
      throw new Error("Failed to ensure organization");
    }

    // 2. Ensure users exist
    const users = await ensureUsers();

    // 3. Ensure employees exist
    const employees = await ensureEmployees(users);

    // 4. Create contacts
    const contacts = await createContacts(organizationId);

    // 5. Create requests
    const requests = await createRequests(organizationId, users);

    // 6. Create case files
    const caseFiles = await createCaseFiles(organizationId, users, employees, requests, contacts);

    console.log("Quebec case files seeding completed successfully!");
    console.log(`Created/verified:`);
    console.log(`- ${users.length} users`);
    console.log(`- ${employees.length} employees`);
    console.log(`- ${contacts.length} contacts`);
    console.log(`- ${requests.length} requests`);
    console.log(`- ${caseFiles.length} case files`);

  } catch (error) {
    console.error("Seeding failed:", error);
    process.exit(1);
  }
}

seedCaseFilesQuebec();
