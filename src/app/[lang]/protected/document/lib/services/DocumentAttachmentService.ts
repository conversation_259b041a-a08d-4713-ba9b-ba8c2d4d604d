import { createClient } from "@/lib/supabase/server";
import { logger } from "@/lib/logger/services/LoggerService";
import { ServiceResponse, successResponse, errorResponse } from "@/lib/types/responses";
import { ProfileService } from "@/app/[lang]/protected/organization/(features)/profile/lib/services/ProfileService";
import { Database } from "@/lib/types/database.types";

type DocumentAttachment = Database["public"]["Tables"]["document_attachments"]["Row"];
type DocumentAttachmentInsert = Database["public"]["Tables"]["document_attachments"]["Insert"];
type DocumentAttachmentUpdate = Database["public"]["Tables"]["document_attachments"]["Update"];

export interface AttachmentListParams {
  page?: number;
  limit?: number;
  search?: string;
  entityType?: string;
  entityId?: string;
  documentType?: string;
  category?: string;
  sortBy?: string;
  sortOrder?: "asc" | "desc";
}

export interface AttachmentUploadParams {
  file: File;
  attachedToType: string;
  attachedToId: string;
  category?: string;
  tags?: string[];
  description?: string;
}

/**
 * Service for managing document attachments
 * Provides methods for uploading, downloading, and managing document attachments
 */
export class DocumentAttachmentService {
  /**
   * Private constructor to prevent direct instantiation
   */
  private constructor() {}

  /**
   * List document attachments with filtering, pagination, and sorting
   */
  static async list(
    params: AttachmentListParams = {}
  ): Promise<ServiceResponse<{ items: DocumentAttachment[]; total: number }>> {
    try {
      const organization = await ProfileService.getCurrentOrganization();
      if (!organization) {
        return errorResponse(null, "Organization not found");
      }

      const {
        page = 1,
        limit = 12,
        search,
        entityType,
        entityId,
        documentType,
        category,
        sortBy = "uploaded_at",
        sortOrder = "desc",
      } = params;

      const supabase = await createClient();
      let query = supabase
        .from("document_attachments")
        .select("*", { count: "exact" })
        .eq("organization_id", organization.id)
        .eq("status", "attached");

      // Apply filters
      if (search) {
        query = query.ilike("document_name", `%${search}%`);
      }

      if (entityType) {
        query = query.eq("attached_to_type", entityType);
      }

      if (entityId) {
        query = query.eq("attached_to_id", entityId);
      }

      if (documentType) {
        query = query.eq("document_type", documentType);
      }

      if (category) {
        query = query.contains("metadata", { category });
      }

      // Apply sorting
      query = query.order(sortBy, { ascending: sortOrder === "asc" });

      // Apply pagination
      const from = (page - 1) * limit;
      const to = from + limit - 1;
      query = query.range(from, to);

      const { data, error, count } = await query;

      if (error) {
        logger.error(`Error listing attachments: ${error.message}`);
        return errorResponse(error, `Failed to list attachments: ${error.message}`);
      }

      return successResponse({
        items: data || [],
        total: count || 0,
      });
    } catch (error) {
      logger.error("Error in DocumentAttachmentService.list:", error as Error);
      return errorResponse(error, "Failed to list attachments");
    }
  }

  /**
   * Get a document attachment by ID
   */
  static async getById(id: string): Promise<ServiceResponse<DocumentAttachment>> {
    try {
      const organization = await ProfileService.getCurrentOrganization();
      if (!organization) {
        return errorResponse(null, "Organization not found");
      }

      const supabase = await createClient();
      const { data, error } = await supabase
        .from("document_attachments")
        .select("*")
        .eq("id", id)
        .eq("organization_id", organization.id)
        .single();

      if (error) {
        logger.error(`Error fetching attachment: ${error.message}`);
        return errorResponse(error, `Failed to fetch attachment: ${error.message}`);
      }

      if (!data) {
        return errorResponse(null, "Attachment not found");
      }

      return successResponse(data);
    } catch (error) {
      logger.error("Error in DocumentAttachmentService.getById:", error as Error);
      return errorResponse(error, "Failed to fetch attachment");
    }
  }

  /**
   * Get attachments for a specific entity
   */
  static async getByEntity(
    entityType: string,
    entityId: string
  ): Promise<ServiceResponse<DocumentAttachment[]>> {
    try {
      const organization = await ProfileService.getCurrentOrganization();
      if (!organization) {
        return errorResponse(null, "Organization not found");
      }

      const supabase = await createClient();
      const { data, error } = await supabase
        .from("document_attachments")
        .select("*")
        .eq("organization_id", organization.id)
        .eq("attached_to_type", entityType)
        .eq("attached_to_id", entityId)
        .eq("status", "attached")
        .order("uploaded_at", { ascending: false });

      if (error) {
        logger.error(`Error fetching entity attachments: ${error.message}`);
        return errorResponse(error, `Failed to fetch entity attachments: ${error.message}`);
      }

      return successResponse(data || []);
    } catch (error) {
      logger.error("Error in DocumentAttachmentService.getByEntity:", error as Error);
      return errorResponse(error, "Failed to fetch entity attachments");
    }
  }

  // REMOVED: uploadFile method - File operations moved to API routes

  // REMOVED: downloadFile method - File operations moved to API routes

  /**
   * Delete an attachment (soft delete)
   */
  static async deleteAttachment(id: string): Promise<ServiceResponse<boolean>> {
    try {
      const organization = await ProfileService.getCurrentOrganization();
      if (!organization) {
        return errorResponse(null, "Organization not found");
      }

      const supabase = await createClient();

      // Soft delete by updating status
      const { error } = await supabase
        .from("document_attachments")
        .update({
          status: "deleted",
          updated_at: new Date().toISOString(),
        })
        .eq("id", id)
        .eq("organization_id", organization.id);

      if (error) {
        logger.error(`Error deleting attachment: ${error.message}`);
        return errorResponse(error, `Failed to delete attachment: ${error.message}`);
      }

      // TODO: Optionally delete file from storage after grace period
      logger.info(`Attachment deleted: ${id}`);
      return successResponse(true);
    } catch (error) {
      logger.error("Error in DocumentAttachmentService.deleteAttachment:", error as Error);
      return errorResponse(error, "Failed to delete attachment");
    }
  }

  /**
   * Update attachment metadata
   */
  static async updateMetadata(
    id: string,
    metadata: { category?: string; tags?: string[]; description?: string }
  ): Promise<ServiceResponse<DocumentAttachment>> {
    try {
      const organization = await ProfileService.getCurrentOrganization();
      if (!organization) {
        return errorResponse(null, "Organization not found");
      }

      const supabase = await createClient();

      // Get current attachment to merge metadata
      const currentResult = await this.getById(id);
      if (!currentResult.success || !currentResult.data) {
        return errorResponse(null, "Attachment not found");
      }

      const currentMetadata = (currentResult.data.metadata as any) || {};
      const updatedMetadata = {
        ...currentMetadata,
        ...metadata,
      };

      const { data, error } = await supabase
        .from("document_attachments")
        .update({
          metadata: updatedMetadata,
          updated_at: new Date().toISOString(),
        })
        .eq("id", id)
        .eq("organization_id", organization.id)
        .select()
        .single();

      if (error) {
        logger.error(`Error updating attachment metadata: ${error.message}`);
        return errorResponse(error, `Failed to update attachment metadata: ${error.message}`);
      }

      return successResponse(data);
    } catch (error) {
      logger.error("Error in DocumentAttachmentService.updateMetadata:", error as Error);
      return errorResponse(error, "Failed to update attachment metadata");
    }
  }

  /**
   * Validate uploaded file
   */
  private static validateFile(file: File): { success: boolean; error?: string } {
    // Check file size (50MB limit)
    const maxSize = 50 * 1024 * 1024; // 50MB
    if (file.size > maxSize) {
      return { success: false, error: "File size exceeds 50MB limit" };
    }

    // Check file type
    const allowedTypes = [
      "application/pdf",
      "application/msword",
      "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
      "image/jpeg",
      "image/jpg",
      "image/png",
      "image/gif",
      "text/plain",
    ];

    if (!allowedTypes.includes(file.type)) {
      return { success: false, error: "File type not allowed" };
    }

    return { success: true };
  }

  // REMOVED: generateSignedUrl method - Security violation, using session-based downloads instead
}
