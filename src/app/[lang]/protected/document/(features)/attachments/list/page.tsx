import { notFound } from "next/navigation";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Upload, FileText } from "lucide-react";
import Link from "next/link";
import { i18n } from "@/lib/i18n/services/I18nService";
import { auth } from "@/lib/authentication/services/AuthenticationService";
import { getAttachments } from "../actions/attachment-actions";
import { AttachmentManagerWrapper } from "../components/AttachmentManagerWrapper";
import { SearchAndFilters } from "../components/SearchAndFilters";
import { AuthorizationRequired } from "@/lib/authorization/components/AuthorizationRequired";
import { DOCUMENT_PERMISSIONS } from "@/app/[lang]/protected/document/lib/security";
import { PageHeader } from "@/components";

interface AttachmentsPageProps {
  params: Promise<{
    lang: string;
  }>;
  searchParams: Promise<{
    page?: string;
    limit?: string;
    search?: string;
    entity_type?: string;
    entity_id?: string;
    document_type?: string;
    sort?: string;
    dir?: string;
  }>;
}

/**
 * Document Attachments Management Page
 * Displays a library of all document attachments with filtering and search
 */
export default async function AttachmentsPage({ params, searchParams }: AttachmentsPageProps) {
  // Await the params and searchParams
  const { lang } = await params;
  const searchParamsData = await searchParams;

  // Get the dictionary
  const dictionary = i18n.getDictionary(lang);

  // Get the current user
  const user = await auth.getCurrentUser();
  if (!user) {
    notFound();
  }

  // Parse search parameters
  const page = searchParamsData.page ? parseInt(searchParamsData.page) : 1;
  const limit = searchParamsData.limit ? parseInt(searchParamsData.limit) : 12;
  const search = searchParamsData.search || "";
  const entityType = searchParamsData.entity_type || undefined;
  const entityId = searchParamsData.entity_id || undefined;
  const documentType = searchParamsData.document_type || undefined;
  const sortBy = searchParamsData.sort || "uploaded_at";
  const sortOrder = searchParamsData.dir || "desc";

  // Call server action to get attachments
  let state;
  try {
    state = await getAttachments({
      page,
      limit,
      search,
      entityType,
      entityId,
      documentType,
      sortBy: sortBy as any,
      sortOrder: sortOrder as any,
    });
  } catch (error) {
    console.error("Error loading attachments:", error);
    state = {
      success: false,
      error: "Failed to load attachments",
      data: { items: [], total: 0 },
    };
  }

  // Extract items and total count
  const items = state.data?.items || [];
  const totalItems = state.data?.total || 0;
  const totalPages = Math.ceil(totalItems / limit);

  return (
    <AuthorizationRequired permission={DOCUMENT_PERMISSIONS.ATTACHMENT.VIEW}>
      <div className="space-y-6">
        {/* Header */}
        <PageHeader
          title={dictionary.documents?.title || "Document Attachments"}
          description={
            dictionary.documents?.description ||
            "Manage and organize document attachments across all entities"
          }
          actions={
            <Button asChild>
              <Link href={`/${lang}/protected/document/attachments/upload`}>
                <Upload className="h-4 w-4 mr-2" />
                {dictionary.documents?.uploadDocuments || "Upload Documents"}
              </Link>
            </Button>
          }
        />

        {/* Filters and Search */}
        <SearchAndFilters
          dictionary={dictionary}
          initialSearch={search}
          initialEntityType={entityType}
          initialDocumentType={documentType}
          initialSort={sortBy}
        />

        {/* Error Display */}
        {!state.success && (
          <Card>
            <CardContent className="pt-6">
              <div className="text-center py-8">
                <div className="text-red-500 mb-2">
                  ⚠️ {dictionary.documents?.errorLoadingAttachments || "Error Loading Attachments"}
                </div>
                <p className="text-muted-foreground">{state.error}</p>
                <p className="text-sm text-muted-foreground mt-2">
                  {dictionary.documents?.checkConfiguration ||
                    "Please check the configuration and try again."}
                </p>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Attachments Grid */}
        {state.success && (
          <AttachmentManagerWrapper
            attachments={items.map((item) => ({
              ...item,
              file_size: item.file_size || 0,
              uploaded_at: item.uploaded_at || new Date().toISOString(),
              metadata: item.metadata as
                | { category?: string; tags?: string[]; description?: string }
                | undefined,
            }))}
            title={dictionary.documents?.title || "Document Attachments"}
            description={
              totalItems > 0
                ? dictionary.documents?.documentsFound?.replace("{count}", totalItems.toString()) ||
                  `${totalItems} documents found`
                : dictionary.documents?.noDocumentsFound || "No documents found"
            }
          />
        )}
      </div>
    </AuthorizationRequired>
  );
}
