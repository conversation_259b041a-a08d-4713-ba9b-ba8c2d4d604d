import { notFound } from "next/navigation";
import { getCaseFileWithRelations } from "../../actions";
import { CaseFileHeader, CaseFileNavigation } from "../../components/navigation";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { H2, H4, P, Muted } from "@/components/typography";
import { CheckCircle, FileText, Calendar, Download, Archive, Users, Settings } from "lucide-react";
import { PageHeader } from "@/components/PageHeader";

interface ClosedCaseFilePageProps {
  params: Promise<{
    lang: string;
    id: string;
  }>;
}

/**
 * Closed Case File Page
 * Displays completed case information and final reports
 */
export default async function ClosedCaseFilePage({ params }: ClosedCaseFilePageProps) {
  const { lang, id } = await params;

  // Get case file data
  const caseFileResponse = await getCaseFileWithRelations(id);

  if (!caseFileResponse.success || !caseFileResponse.data) {
    notFound();
  }

  const caseFile = caseFileResponse.data;

  // Mock closure information
  const closureInfo = {
    reason: "Services completed successfully",
    closedDate: caseFile.closed_at || new Date().toISOString(),
    closedBy: "John Doe",
    outcome: "Positive",
    notes:
      "Family has successfully completed all required services. Children are thriving in their current environment. No further intervention required at this time.",
  };

  // Mock final reports and documents
  const finalDocuments = [
    {
      id: "final_report",
      title: "Final Case Report",
      description: "Comprehensive summary of case progression and outcomes",
      type: "report",
      date: closureInfo.closedDate,
      size: "2.4 MB",
    },
    {
      id: "service_summary",
      title: "Service Summary",
      description: "Summary of all services provided during case duration",
      type: "summary",
      date: closureInfo.closedDate,
      size: "1.8 MB",
    },
    {
      id: "family_assessment",
      title: "Final Family Assessment",
      description: "Final assessment of family functioning and wellbeing",
      type: "assessment",
      date: closureInfo.closedDate,
      size: "3.1 MB",
    },
    {
      id: "closure_checklist",
      title: "Closure Checklist",
      description: "Completed checklist verifying all closure requirements met",
      type: "checklist",
      date: closureInfo.closedDate,
      size: "0.5 MB",
    },
  ];

  // Mock case statistics
  const caseStatistics = {
    totalDuration: Math.floor(
      (new Date(closureInfo.closedDate).getTime() - new Date(caseFile.created_at).getTime()) /
        (1000 * 60 * 60 * 24)
    ),
    servicesProvided: 8,
    appointmentsCompleted: 24,
    documentsGenerated: 15,
  };

  return (
    <div className="space-y-6">
      {/* Case File Header */}
      <CaseFileHeader
        caseFileId={caseFile.id}
        caseNumber={caseFile.case_number}
        status={caseFile.status}
        activatedAt={caseFile.activated_at}
        assignedEmployee={caseFile.employees}
        lang={lang}
        showBreadcrumbs={true}
      />

      {/* Case File Navigation */}
      <CaseFileNavigation caseFileId={caseFile.id} currentStatus={caseFile.status} lang={lang} />

      {/* Closure Information */}
      <Card>
        <CardHeader>
          <CardTitle>
            <CheckCircle />
            Case Closure Information
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Muted>Closure Reason</Muted>
                <P>{closureInfo.reason}</P>
              </div>
              <div>
                <Muted>Closed Date</Muted>
                <P>{new Date(closureInfo.closedDate).toLocaleDateString()}</P>
              </div>
              <div>
                <Muted>Closed By</Muted>
                <P>{closureInfo.closedBy}</P>
              </div>
              <div>
                <Muted>Outcome</Muted>
                <Badge variant={closureInfo.outcome === "Positive" ? "default" : "secondary"}>
                  {closureInfo.outcome}
                </Badge>
              </div>
            </div>

            {closureInfo.notes && (
              <div>
                <Muted>Closure Notes</Muted>
                <Card>
                  <CardContent>
                    <P>{closureInfo.notes}</P>
                  </CardContent>
                </Card>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Case Statistics */}
      <Card>
        <CardHeader>
          <CardTitle>
            <Archive />
            Case Statistics
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <Card>
              <CardContent>
                <H2>{caseStatistics.totalDuration}</H2>
                <Muted>Days Active</Muted>
              </CardContent>
            </Card>
            <Card>
              <CardContent>
                <H2>{caseStatistics.servicesProvided}</H2>
                <Muted>Services Provided</Muted>
              </CardContent>
            </Card>
            <Card>
              <CardContent>
                <H2>{caseStatistics.appointmentsCompleted}</H2>
                <Muted>Appointments</Muted>
              </CardContent>
            </Card>
            <Card>
              <CardContent>
                <H2>{caseStatistics.documentsGenerated}</H2>
                <Muted>Documents</Muted>
              </CardContent>
            </Card>
          </div>
        </CardContent>
      </Card>

      {/* Final Documents */}
      <Card>
        <CardHeader>
          <CardTitle>
            <FileText />
            Final Documents & Reports
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {finalDocuments.map((document) => (
              <Card key={document.id}>
                <CardContent>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center">
                      <FileText />
                      <div>
                        <H4>{document.title}</H4>
                        <P>{document.description}</P>
                        <Muted>
                          {new Date(document.date).toLocaleDateString()} • {document.size}
                        </Muted>
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      <Badge variant="secondary">{document.type}</Badge>
                      <Button variant="outline" size="sm">
                        <Download />
                        Download
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Family Information at Closure */}
      <Card>
        <CardHeader>
          <CardTitle>
            <Users />
            Family Information at Closure
          </CardTitle>
        </CardHeader>
        <CardContent>
          {caseFile.contacts && caseFile.contacts.length > 0 ? (
            <div className="space-y-3">
              {caseFile.contacts.map((contact) => (
                <Card key={contact.id}>
                  <CardContent>
                    <div className="flex items-center justify-between">
                      <div>
                        <H4>{contact.name}</H4>
                        <Muted>
                          {contact.email || "No email"} • {contact.phone || "No phone"}
                        </Muted>
                      </div>
                      <Badge variant="outline">Archived</Badge>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          ) : (
            <div className="text-center py-8">
              <Users />
              <Muted>No family contacts on record</Muted>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Action Buttons */}
      <div className="flex justify-between">
        <div className="flex gap-2">
          <Button variant="outline">
            <Download />
            Download All Documents
          </Button>
          <Button variant="outline">
            <Archive />
            Archive Case
          </Button>
        </div>
        <div className="flex gap-2">
          <Button variant="outline">Print Summary</Button>
          <Button variant="outline">
            <Calendar />
            Schedule Follow-up
          </Button>
        </div>
      </div>
    </div>
  );
}
