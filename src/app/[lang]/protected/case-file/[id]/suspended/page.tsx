import { notFound } from "next/navigation";
import { getCaseFileWithRelations } from "../../actions";
import { CaseFileHeader, CaseFileNavigation } from "../../components/navigation";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { H2, P } from "@/components/typography";
import { AlertTriangle, Clock, FileText, Play, Calendar } from "lucide-react";

interface SuspendedCaseFilePageProps {
  params: Promise<{
    lang: string;
    id: string;
  }>;
}

/**
 * Suspended Case File Page
 * Displays case files in suspended state with reactivation options
 */
export default async function SuspendedCaseFilePage({ params }: SuspendedCaseFilePageProps) {
  const { lang, id } = await params;

  // Get case file data
  const caseFileResponse = await getCaseFileWithRelations(id, true, false);

  if (!caseFileResponse.success || !caseFileResponse.data) {
    notFound();
  }

  const caseFile = caseFileResponse.data;

  // Mock suspension reasons and requirements for reactivation
  const suspensionInfo = {
    reason: "Family temporarily relocated",
    suspendedDate: caseFile.suspended_at || new Date().toISOString(),
    suspendedBy: "Jane Smith",
    notes:
      "Family has temporarily moved to another province for work. Expected to return in 3-6 months.",
  };

  const reactivationRequirements = [
    {
      id: "contact_verification",
      title: "Verify Current Contact Information",
      description: "Confirm family has returned and update contact details",
      completed: false,
      required: true,
    },
    {
      id: "address_verification",
      title: "Verify Current Address",
      description: "Confirm current residential address and living situation",
      completed: false,
      required: true,
    },
    {
      id: "needs_assessment",
      title: "Updated Needs Assessment",
      description: "Reassess family needs and service requirements",
      completed: false,
      required: true,
    },
    {
      id: "service_plan_review",
      title: "Review Service Plan",
      description: "Update service plan based on current circumstances",
      completed: false,
      required: false,
    },
  ];

  const completedRequirements = reactivationRequirements.filter((req) => req.completed).length;
  const totalRequirements = reactivationRequirements.length;
  const requiredCompleted = reactivationRequirements
    .filter((req) => req.required)
    .every((req) => req.completed);

  return (
    <div className="space-y-6">
      {/* Case File Header */}
      <CaseFileHeader
        caseFileId={caseFile.id}
        caseNumber={caseFile.case_number}
        status={caseFile.status}
        activatedAt={caseFile.activated_at}
        assignedEmployee={caseFile.employees}
        lang={lang}
        showBreadcrumbs={true}
      />

      {/* Case File Navigation */}
      <CaseFileNavigation caseFileId={caseFile.id} currentStatus={caseFile.status} lang={lang} />

      {/* Suspension Information */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <AlertTriangle className="h-5 w-5 mr-2 text-yellow-600" />
            Suspension Information
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <H2 className="text-sm font-medium text-muted-foreground">Suspension Reason</H2>
                <P className="mt-1">{suspensionInfo.reason}</P>
              </div>
              <div>
                <H2 className="text-sm font-medium text-muted-foreground">Suspended Date</H2>
                <P className="mt-1">
                  {new Date(suspensionInfo.suspendedDate).toLocaleDateString()}
                </P>
              </div>
              <div>
                <H2 className="text-sm font-medium text-muted-foreground">Suspended By</H2>
                <P className="mt-1">{suspensionInfo.suspendedBy}</P>
              </div>
              <div>
                <H2 className="text-sm font-medium text-muted-foreground">Duration</H2>
                <P className="mt-1">
                  {Math.floor(
                    (new Date().getTime() - new Date(suspensionInfo.suspendedDate).getTime()) /
                      (1000 * 60 * 60 * 24)
                  )}{" "}
                  days
                </P>
              </div>
            </div>

            {suspensionInfo.notes && (
              <div>
                <H2 className="text-sm font-medium text-muted-foreground">Notes</H2>
                <P className="mt-1 text-sm bg-gray-50 p-3 rounded-lg">{suspensionInfo.notes}</P>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Reactivation Progress */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Play className="h-5 w-5 mr-2" />
            Reactivation Progress
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="mb-4">
            <div className="flex justify-between items-center mb-2">
              <P className="text-sm font-medium">Requirements Completed</P>
              <P className="text-sm text-muted-foreground">
                {completedRequirements}/{totalRequirements} completed
              </P>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div
                className="bg-green-600 h-2 rounded-full transition-all duration-300"
                style={{ width: `${(completedRequirements / totalRequirements) * 100}%` }}
              />
            </div>
          </div>

          {requiredCompleted && (
            <div className="mb-4 p-3 bg-green-50 border border-green-200 rounded-lg">
              <div className="flex items-center">
                <Play className="h-5 w-5 text-green-600 mr-2" />
                <P className="text-sm font-medium text-green-800">
                  Ready to reactivate! All required items completed.
                </P>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Reactivation Checklist */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <FileText className="h-5 w-5 mr-2" />
            Reactivation Checklist
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {reactivationRequirements.map((requirement) => (
              <div
                key={requirement.id}
                className="flex items-start space-x-3 p-3 border rounded-lg"
              >
                <div className="flex-shrink-0 mt-1">
                  {requirement.completed ? (
                    <div className="h-5 w-5 bg-green-600 rounded-full flex items-center justify-center">
                      <svg className="h-3 w-3 text-white" fill="currentColor" viewBox="0 0 20 20">
                        <path
                          fillRule="evenodd"
                          d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                          clipRule="evenodd"
                        />
                      </svg>
                    </div>
                  ) : (
                    <div className="h-5 w-5 border-2 border-gray-300 rounded-full" />
                  )}
                </div>
                <div className="flex-1">
                  <div className="flex items-center gap-2">
                    <H2
                      className={`text-base font-medium ${
                        requirement.completed ? "text-green-800" : "text-gray-900"
                      }`}
                    >
                      {requirement.title}
                    </H2>
                    {requirement.required && (
                      <Badge variant="secondary" className="text-xs">
                        Required
                      </Badge>
                    )}
                  </div>
                  <P className="text-sm text-muted-foreground mt-1">{requirement.description}</P>
                </div>
                <div className="flex-shrink-0">
                  <Button
                    variant={requirement.completed ? "outline" : "default"}
                    size="sm"
                    disabled={requirement.completed}
                  >
                    {requirement.completed ? "Completed" : "Start"}
                  </Button>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Recent Activity During Suspension */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Clock className="h-5 w-5 mr-2" />
            Activity During Suspension
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
              <div className="flex items-center">
                <Calendar className="h-4 w-4 text-blue-500 mr-2" />
                <span className="text-sm">Follow-up call scheduled</span>
              </div>
              <span className="text-xs text-muted-foreground">2 weeks ago</span>
            </div>
            <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
              <div className="flex items-center">
                <FileText className="h-4 w-4 text-green-500 mr-2" />
                <span className="text-sm">Suspension documentation completed</span>
              </div>
              <span className="text-xs text-muted-foreground">
                {Math.floor(
                  (new Date().getTime() - new Date(suspensionInfo.suspendedDate).getTime()) /
                    (1000 * 60 * 60 * 24)
                )}{" "}
                days ago
              </span>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Action Buttons */}
      <div className="flex justify-between">
        <div className="flex gap-2">
          <Button variant="outline">Add Note</Button>
          <Button variant="outline">Schedule Follow-up</Button>
        </div>
        <div className="flex gap-2">
          <Button variant="outline">Close Case File</Button>
          <Button
            disabled={!requiredCompleted}
            className={requiredCompleted ? "bg-green-600 hover:bg-green-700" : ""}
          >
            Reactivate Case File
          </Button>
        </div>
      </div>
    </div>
  );
}
