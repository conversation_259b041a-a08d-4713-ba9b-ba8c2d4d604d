import { notFound } from "next/navigation";
import { getCaseFileWithRelations } from "../../actions";
import { CaseFileNavigation } from "../../components/navigation";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { H2, P } from "@/components/typography";
import { AlertTriangle, Clock, FileText, Settings, RotateCcw, Play, Calendar } from "lucide-react";
import { PageHeader } from "@/components/PageHeader";

interface SuspendedCaseFilePageProps {
  params: Promise<{
    lang: string;
    id: string;
  }>;
}

/**
 * Suspended Case File Page
 * Displays case files in suspended state with reactivation options
 */
export default async function SuspendedCaseFilePage({ params }: SuspendedCaseFilePageProps) {
  const { lang, id } = await params;

  // Get case file data
  const caseFileResponse = await getCaseFileWithRelations(id);

  if (!caseFileResponse.success || !caseFileResponse.data) {
    notFound();
  }

  const caseFile = caseFileResponse.data;

  // Mock suspension reasons and requirements for reactivation
  const suspensionInfo = {
    reason: "Family temporarily relocated",
    suspendedDate: caseFile.suspended_at || new Date().toISOString(),
    suspendedBy: "Jane Smith",
    notes:
      "Family has temporarily moved to another province for work. Expected to return in 3-6 months.",
  };

  const reactivationRequirements = [
    {
      id: "contact_verification",
      title: "Verify Current Contact Information",
      description: "Confirm family has returned and update contact details",
      completed: false,
      required: true,
    },
    {
      id: "address_verification",
      title: "Verify Current Address",
      description: "Confirm current residential address and living situation",
      completed: false,
      required: true,
    },
    {
      id: "needs_assessment",
      title: "Updated Needs Assessment",
      description: "Reassess family needs and service requirements",
      completed: false,
      required: true,
    },
    {
      id: "service_plan_review",
      title: "Review Service Plan",
      description: "Update service plan based on current circumstances",
      completed: false,
      required: false,
    },
  ];

  const completedRequirements = reactivationRequirements.filter((req) => req.completed).length;
  const totalRequirements = reactivationRequirements.length;
  const requiredCompleted = reactivationRequirements
    .filter((req) => req.required)
    .every((req) => req.completed);

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <PageHeader
        title={`Case File ${caseFile.case_number} - Suspended`}
        description="Case file is temporarily suspended - complete requirements to reactivate"
        actions={
          <div className="flex gap-2">
            <Button variant="outline">
              <Settings className="mr-2" />
              Settings
            </Button>
            <Button disabled={!requiredCompleted}>
              <RotateCcw className="mr-2" />
              Reactivate
            </Button>
          </div>
        }
      />

      {/* Case File Navigation */}
      <CaseFileNavigation caseFileId={caseFile.id} currentStatus={caseFile.status} lang={lang} />

      {/* Suspension Information */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <AlertTriangle className="mr-2" />
            Suspension Information
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <H2>Suspension Reason</H2>
                <P>{suspensionInfo.reason}</P>
              </div>
              <div>
                <H2>Suspended Date</H2>
                <P>
                  {new Date(suspensionInfo.suspendedDate).toLocaleDateString()}
                </P>
              </div>
              <div>
                <H2>Suspended By</H2>
                <P>{suspensionInfo.suspendedBy}</P>
              </div>
              <div>
                <H2>Duration</H2>
                <P>
                  {Math.floor(
                    (new Date().getTime() - new Date(suspensionInfo.suspendedDate).getTime()) /
                      (1000 * 60 * 60 * 24)
                  )}{" "}
                  days
                </P>
              </div>
            </div>

            {suspensionInfo.notes && (
              <div>
                <H2>Notes</H2>
                <P>{suspensionInfo.notes}</P>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Reactivation Progress */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Play className="mr-2" />
            Reactivation Progress
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div>
            <div className="flex justify-between items-center">
              <P>Requirements Completed</P>
              <P>
                {completedRequirements}/{totalRequirements} completed
              </P>
            </div>
          </div>

          {requiredCompleted && (
            <div>
              <div className="flex items-center">
                <Play className="mr-2" />
                <P>
                  Ready to reactivate! All required items completed.
                </P>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Reactivation Checklist */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <FileText className="mr-2" />
            Reactivation Checklist
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {reactivationRequirements.map((requirement) => (
              <div
                key={requirement.id}
                className="flex items-start space-x-3"
              >
                <div>
                  {requirement.completed ? (
                    <div>
                      <svg fill="currentColor" viewBox="0 0 20 20">
                        <path
                          fillRule="evenodd"
                          d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                          clipRule="evenodd"
                        />
                      </svg>
                    </div>
                  ) : (
                    <div />
                  )}
                </div>
                <div>
                  <div className="flex items-center gap-2">
                    <H2>
                      {requirement.title}
                    </H2>
                    {requirement.required && (
                      <Badge variant="secondary">
                        Required
                      </Badge>
                    )}
                  </div>
                  <P>{requirement.description}</P>
                </div>
                <div>
                  <Button
                    variant={requirement.completed ? "outline" : "default"}
                    disabled={requirement.completed}
                  >
                    {requirement.completed ? "Completed" : "Start"}
                  </Button>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Recent Activity During Suspension */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Clock className="mr-2" />
            Activity During Suspension
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <div className="flex items-center">
                <Calendar className="mr-2" />
                <span>Follow-up call scheduled</span>
              </div>
              <span>2 weeks ago</span>
            </div>
            <div className="flex items-center justify-between">
              <div className="flex items-center">
                <FileText className="mr-2" />
                <span>Suspension documentation completed</span>
              </div>
              <span>
                {Math.floor(
                  (new Date().getTime() - new Date(suspensionInfo.suspendedDate).getTime()) /
                    (1000 * 60 * 60 * 24)
                )}{" "}
                days ago
              </span>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
