"use client";

import { useEffect, useState } from "react";
import Link from "next/link";
import { motion } from "framer-motion";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { H2, H3, P } from "@/components/typography";
import { ContactAvatar } from "../../../components/contacts/ContactAvatar";
import {
  FileText,
  Download,
  Eye,
  CheckCircle,
  Clock,
  AlertCircle,
  ExternalLink,
} from "lucide-react";
import { CaseFileWithRelations } from "../../../../../lib/types";
import { DocumentService, DocumentWithSignature } from "../../../lib/documentService";

interface Contact {
  id: string;
  name: string;
  email?: any;
  phone?: any;
  relationship_type: string;
}

interface ContactDocumentWorkflowProps {
  contact: Contact;
  caseFile: CaseFileWithRelations;
  lang: string;
}

/**
 * Contact-specific document workflow component
 * Shows documents and required actions for a selected contact
 */
export function ContactDocumentWorkflow({ contact, caseFile, lang }: ContactDocumentWorkflowProps) {
  const [documents, setDocuments] = useState<DocumentWithSignature[]>([]);
  const [loading, setLoading] = useState(true);

  // Fetch documents for this contact
  useEffect(() => {
    const fetchDocuments = async () => {
      try {
        setLoading(true);
        const contactDocuments = await DocumentService.getDocumentsForContact(
          caseFile.id,
          contact.id
        );
        setDocuments(contactDocuments);
      } catch (error) {
        console.error("Error fetching documents:", error);
      } finally {
        setLoading(false);
      }
    };

    fetchDocuments();
  }, [caseFile.id, contact.id]);

  const getStatusBadge = (document: DocumentWithSignature) => {
    const status = DocumentService.getDocumentStatus(document);

    switch (status) {
      case "completed":
        return (
          <Badge variant="default" className="bg-green-100 text-green-800 border-green-200">
            <CheckCircle className="h-3 w-3 mr-1" />
            Completed
          </Badge>
        );
      case "pending_signature":
        return (
          <Badge variant="default" className="bg-orange-100 text-orange-800 border-orange-200">
            <Clock className="h-3 w-3 mr-1" />
            Needs Signature
          </Badge>
        );
      case "rejected":
        return (
          <Badge variant="default" className="bg-red-100 text-red-800 border-red-200">
            <AlertCircle className="h-3 w-3 mr-1" />
            Rejected
          </Badge>
        );
      case "expired":
        return (
          <Badge variant="default" className="bg-gray-100 text-gray-800 border-gray-200">
            <Clock className="h-3 w-3 mr-1" />
            Expired
          </Badge>
        );
      default:
        return (
          <Badge variant="secondary">
            <Eye className="h-3 w-3 mr-1" />
            Review
          </Badge>
        );
    }
  };

  const getActionButton = (document: DocumentWithSignature) => {
    const status = DocumentService.getDocumentStatus(document);
    const requiresAction = DocumentService.requiresAction(document);

    return (
      <Link href={`/${lang}/protected/document/attachments/${document.id}/view`}>
        <Button
          size="sm"
          variant={requiresAction ? "default" : "outline"}
          className="flex items-center gap-2"
        >
          {status === "completed" ? (
            <>
              <Eye className="h-4 w-4" />
              View Document
            </>
          ) : requiresAction ? (
            <>
              <FileText className="h-4 w-4" />
              Sign Document
            </>
          ) : (
            <>
              <Eye className="h-4 w-4" />
              View Document
            </>
          )}
          <ExternalLink className="h-3 w-3" />
        </Button>
      </Link>
    );
  };

  return (
    <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
      {/* Left Sidebar - Contact Info */}
      <motion.div
        initial={{ opacity: 0, x: -40 }}
        animate={{ opacity: 1, x: 0 }}
        transition={{ duration: 1.2, ease: [0.25, 0.46, 0.45, 0.94] }}
        className="lg:col-span-1"
      >
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-3">
              <ContactAvatar name={contact.name} />
              <div>
                <H3>{contact.name}</H3>
                <Badge variant="secondary" className="text-xs">
                  {contact.relationship_type}
                </Badge>
              </div>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <div>
                <P className="text-sm font-medium">Case File</P>
                <P className="text-sm text-muted-foreground">{caseFile.case_number}</P>
              </div>
              {contact.email && (
                <div>
                  <P className="text-sm font-medium">Email</P>
                  <P className="text-sm text-muted-foreground">
                    {typeof contact.email === "string"
                      ? contact.email
                      : contact.email.personal || contact.email.work}
                  </P>
                </div>
              )}
              {contact.phone && (
                <div>
                  <P className="text-sm font-medium">Phone</P>
                  <P className="text-sm text-muted-foreground">
                    {typeof contact.phone === "string"
                      ? contact.phone
                      : contact.phone.mobile || contact.phone.home}
                  </P>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      </motion.div>

      {/* Right Main Content - Documents */}
      <motion.div
        initial={{ opacity: 0, x: 40 }}
        animate={{ opacity: 1, x: 0 }}
        transition={{ duration: 1.2, delay: 0.4, ease: [0.25, 0.46, 0.45, 0.94] }}
        className="lg:col-span-3"
      >
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <FileText className="h-5 w-5" />
              Documents & Actions
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <H2>Required Actions for {contact.name}</H2>

              <div className="space-y-3">
                {documents.map((document, index) => (
                  <motion.div
                    key={document.id}
                    initial={{ opacity: 0, y: 30 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{
                      duration: 0.8,
                      delay: 0.8 + index * 0.2,
                      ease: [0.25, 0.46, 0.45, 0.94],
                    }}
                  >
                    <Card className="border-l-4 border-l-blue-200">
                      <CardContent className="p-4">
                        <div className="flex items-start justify-between">
                          <div className="flex-1">
                            <div className="flex items-center gap-3 mb-2">
                              <H3>{document.name}</H3>
                              {getStatusBadge(document.status)}
                              {document.required && (
                                <Badge variant="outline" className="text-xs">
                                  Required
                                </Badge>
                              )}
                            </div>
                            <P className="text-sm text-muted-foreground">{document.description}</P>
                          </div>
                          <div className="ml-4">{getActionButton(document)}</div>
                        </div>
                      </CardContent>
                    </Card>
                  </motion.div>
                ))}
              </div>

              {/* Progress Summary */}
              <motion.div
                initial={{ opacity: 0, y: 30 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 1.0, delay: 1.6, ease: [0.25, 0.46, 0.45, 0.94] }}
                className="mt-6 p-4 bg-blue-50 rounded-lg border border-blue-200"
              >
                <div className="flex items-center justify-between">
                  <div>
                    <P className="font-medium">Progress Summary</P>
                    <P className="text-sm text-muted-foreground">
                      {documents.filter((d) => d.status === "completed").length} of{" "}
                      {documents.filter((d) => d.required).length} required documents completed
                    </P>
                  </div>
                  <div className="text-right">
                    <P className="text-2xl font-bold text-blue-600">
                      {Math.round(
                        (documents.filter((d) => d.status === "completed").length /
                          documents.filter((d) => d.required).length) *
                          100
                      )}
                      %
                    </P>
                    <P className="text-xs text-muted-foreground">Complete</P>
                  </div>
                </div>
              </motion.div>
            </div>
          </CardContent>
        </Card>
      </motion.div>
    </div>
  );
}
