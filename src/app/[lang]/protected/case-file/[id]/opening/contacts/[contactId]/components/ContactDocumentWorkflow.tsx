"use client";

import { motion } from "framer-motion";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { H2, H3, P } from "@/components/typography";
import { ContactAvatar } from "../../../components/contacts/ContactAvatar";
import { FileText, Download, Eye, CheckCircle, Clock, AlertCircle } from "lucide-react";
import { CaseFileWithRelations } from "../../../../../lib/types";

interface Contact {
  id: string;
  name: string;
  email?: any;
  phone?: any;
  relationship_type: string;
}

interface ContactDocumentWorkflowProps {
  contact: Contact;
  caseFile: CaseFileWithRelations;
}

/**
 * Contact-specific document workflow component
 * Shows documents and required actions for a selected contact
 */
export function ContactDocumentWorkflow({ contact, caseFile }: ContactDocumentWorkflowProps) {
  // Mock document data - replace with real data later
  const documents = [
    {
      id: "1",
      name: "Consent Form",
      type: "consent",
      status: "pending_signature",
      required: true,
      description: "Required consent form for service provision"
    },
    {
      id: "2", 
      name: "Service Agreement",
      type: "agreement",
      status: "pending_review",
      required: true,
      description: "Service agreement outlining terms and conditions"
    },
    {
      id: "3",
      name: "Emergency Contact Information",
      type: "information",
      status: "completed",
      required: true,
      description: "Emergency contact details and medical information"
    },
    {
      id: "4",
      name: "Additional Notes",
      type: "notes",
      status: "optional",
      required: false,
      description: "Optional additional information and notes"
    }
  ];

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "completed":
        return (
          <Badge variant="default" className="bg-green-100 text-green-800 border-green-200">
            <CheckCircle className="h-3 w-3 mr-1" />
            Completed
          </Badge>
        );
      case "pending_signature":
        return (
          <Badge variant="default" className="bg-orange-100 text-orange-800 border-orange-200">
            <Clock className="h-3 w-3 mr-1" />
            Needs Signature
          </Badge>
        );
      case "pending_review":
        return (
          <Badge variant="default" className="bg-blue-100 text-blue-800 border-blue-200">
            <Eye className="h-3 w-3 mr-1" />
            Needs Review
          </Badge>
        );
      default:
        return (
          <Badge variant="secondary">
            <AlertCircle className="h-3 w-3 mr-1" />
            Optional
          </Badge>
        );
    }
  };

  const getActionButton = (document: any) => {
    switch (document.status) {
      case "completed":
        return (
          <Button variant="outline" size="sm">
            <Download className="h-4 w-4 mr-2" />
            Download
          </Button>
        );
      case "pending_signature":
        return (
          <Button size="sm">
            <FileText className="h-4 w-4 mr-2" />
            Sign Document
          </Button>
        );
      case "pending_review":
        return (
          <Button size="sm">
            <Eye className="h-4 w-4 mr-2" />
            Review & Approve
          </Button>
        );
      default:
        return (
          <Button variant="outline" size="sm">
            <Eye className="h-4 w-4 mr-2" />
            View
          </Button>
        );
    }
  };

  return (
    <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
      {/* Left Sidebar - Contact Info */}
      <motion.div
        initial={{ opacity: 0, x: -20 }}
        animate={{ opacity: 1, x: 0 }}
        transition={{ duration: 0.5 }}
        className="lg:col-span-1"
      >
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-3">
              <ContactAvatar name={contact.name} />
              <div>
                <H3>{contact.name}</H3>
                <Badge variant="secondary" className="text-xs">
                  {contact.relationship_type}
                </Badge>
              </div>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <div>
                <P className="text-sm font-medium">Case File</P>
                <P className="text-sm text-muted-foreground">{caseFile.case_number}</P>
              </div>
              {contact.email && (
                <div>
                  <P className="text-sm font-medium">Email</P>
                  <P className="text-sm text-muted-foreground">
                    {typeof contact.email === 'string' ? contact.email : contact.email.personal || contact.email.work}
                  </P>
                </div>
              )}
              {contact.phone && (
                <div>
                  <P className="text-sm font-medium">Phone</P>
                  <P className="text-sm text-muted-foreground">
                    {typeof contact.phone === 'string' ? contact.phone : contact.phone.mobile || contact.phone.home}
                  </P>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      </motion.div>

      {/* Right Main Content - Documents */}
      <motion.div
        initial={{ opacity: 0, x: 20 }}
        animate={{ opacity: 1, x: 0 }}
        transition={{ duration: 0.5, delay: 0.2 }}
        className="lg:col-span-3"
      >
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <FileText className="h-5 w-5" />
              Documents & Actions
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <H2>Required Actions for {contact.name}</H2>
              
              <div className="space-y-3">
                {documents.map((document, index) => (
                  <motion.div
                    key={document.id}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.3, delay: index * 0.1 }}
                  >
                    <Card className="border-l-4 border-l-blue-200">
                      <CardContent className="p-4">
                        <div className="flex items-start justify-between">
                          <div className="flex-1">
                            <div className="flex items-center gap-3 mb-2">
                              <H3>{document.name}</H3>
                              {getStatusBadge(document.status)}
                              {document.required && (
                                <Badge variant="outline" className="text-xs">
                                  Required
                                </Badge>
                              )}
                            </div>
                            <P className="text-sm text-muted-foreground">
                              {document.description}
                            </P>
                          </div>
                          <div className="ml-4">
                            {getActionButton(document)}
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  </motion.div>
                ))}
              </div>

              {/* Progress Summary */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.6 }}
                className="mt-6 p-4 bg-blue-50 rounded-lg border border-blue-200"
              >
                <div className="flex items-center justify-between">
                  <div>
                    <P className="font-medium">Progress Summary</P>
                    <P className="text-sm text-muted-foreground">
                      {documents.filter(d => d.status === 'completed').length} of {documents.filter(d => d.required).length} required documents completed
                    </P>
                  </div>
                  <div className="text-right">
                    <P className="text-2xl font-bold text-blue-600">
                      {Math.round((documents.filter(d => d.status === 'completed').length / documents.filter(d => d.required).length) * 100)}%
                    </P>
                    <P className="text-xs text-muted-foreground">Complete</P>
                  </div>
                </div>
              </motion.div>
            </div>
          </CardContent>
        </Card>
      </motion.div>
    </div>
  );
}
