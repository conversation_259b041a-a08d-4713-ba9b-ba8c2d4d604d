import { notFound } from "next/navigation";
import Link from "next/link";
import { getCaseFileWithRelations } from "../../../../actions";
import { EnhancedPageHeader } from "../../components/EnhancedPageHeader";
import { ContactDocumentWorkflow } from "./components/ContactDocumentWorkflow";
import { PageTransition } from "../../components/PageTransition";
import { ArrowLeft } from "lucide-react";
import { Button } from "@/components/ui/button";

interface ContactPageProps {
  params: Promise<{
    lang: string;
    id: string;
    contactId: string;
  }>;
}

/**
 * Contact-specific document workflow page
 * Shows documents and actions for a selected contact
 */
export default async function ContactPage({ params }: ContactPageProps) {
  const { lang, id, contactId } = await params;

  // Get case file data
  const caseFileResponse = await getCaseFileWithRelations(id);

  if (!caseFileResponse.success || !caseFileResponse.data) {
    notFound();
  }

  const caseFile = caseFileResponse.data;

  // Find the selected contact
  const selectedContact = caseFile.contacts?.find((contact) => contact.id === contactId);

  if (!selectedContact) {
    notFound();
  }

  return (
    <PageTransition direction="forward">
      <div className="space-y-6">
        {/* Enhanced Page Header */}
        <EnhancedPageHeader caseFile={caseFile} />

        {/* Back Navigation */}
        <div className="flex items-center gap-4">
          <Link href={`/${lang}/protected/case-file/${id}/opening/contacts`}>
            <Button variant="outline" size="sm" className="flex items-center gap-2">
              <ArrowLeft className="h-4 w-4" />
              Back to Contacts
            </Button>
          </Link>
          <div className="h-4 w-px bg-gray-300" />
          <span className="text-sm text-muted-foreground">
            Viewing documents for <strong>{selectedContact.name}</strong>
          </span>
        </div>

        {/* Contact Document Workflow */}
        <ContactDocumentWorkflow contact={selectedContact} caseFile={caseFile} lang={lang} />
      </div>
    </PageTransition>
  );
}
