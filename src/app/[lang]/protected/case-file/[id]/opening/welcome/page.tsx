import { notFound } from "next/navigation";
import { getCaseFileWithRelations } from "../../../actions";
import { WelcomeStep } from "../components/wizard/WelcomeStep";
import { EnhancedPageHeader } from "../components/EnhancedPageHeader";
import { WizardBreadcrumb } from "../components/WizardBreadcrumb";
import { PageTransition } from "../components/PageTransition";

interface WelcomePageProps {
  params: Promise<{
    lang: string;
    id: string;
  }>;
}

/**
 * Welcome step of the opening case file wizard
 * First step that introduces the document workflow
 */
export default async function WelcomePage({ params }: WelcomePageProps) {
  const { lang, id } = await params;

  // Get case file data for header
  const caseFileResponse = await getCaseFileWithRelations(id);

  if (!caseFileResponse.success || !caseFileResponse.data) {
    notFound();
  }

  const caseFile = caseFileResponse.data;

  return (
    <div className="space-y-6">
      {/* Enhanced Page Header with Request Details */}
      <EnhancedPageHeader caseFile={caseFile} />

      {/* Welcome Step Component */}
      <WelcomeStep caseFileId={id} lang={lang} />
    </div>
  );
}
