import { createClient } from "@/lib/supabase/server";
import { ProfileService } from "@/app/[lang]/protected/organization/(features)/profile/lib/services/ProfileService";

/**
 * Service for managing the opening case file wizard state
 * Uses metadata to track wizard completion status
 */
export class WizardService {
  /**
   * Check if the wizard has been completed for a case file
   * @param caseFileId The case file ID
   * @returns Promise<boolean> - true if wizard is completed
   */
  static async checkWizardStatus(caseFileId: string): Promise<boolean> {
    try {
      const organization = await ProfileService.getCurrentOrganization();
      if (!organization) {
        return false;
      }

      const supabase = await createClient();
      const { data, error } = await supabase
        .from("case_files")
        .select("metadata")
        .eq("id", caseFileId)
        .eq("organization_id", organization.id)
        .single();

      if (error || !data) {
        return false;
      }

      const metadata = data.metadata as any;
      return metadata?.opening_wizard_completed === true;
    } catch (error) {
      console.error("Error checking wizard status:", error);
      return false;
    }
  }

  /**
   * Mark the wizard as completed for a case file
   * @param caseFileId The case file ID
   * @returns Promise<boolean> - true if successful
   */
  static async markWizardComplete(caseFileId: string): Promise<boolean> {
    try {
      const organization = await ProfileService.getCurrentOrganization();
      if (!organization) {
        return false;
      }

      const supabase = await createClient();
      
      // Get current metadata
      const { data: currentData, error: fetchError } = await supabase
        .from("case_files")
        .select("metadata")
        .eq("id", caseFileId)
        .eq("organization_id", organization.id)
        .single();

      if (fetchError) {
        console.error("Error fetching current metadata:", fetchError);
        return false;
      }

      const currentMetadata = (currentData?.metadata as any) || {};
      const updatedMetadata = {
        ...currentMetadata,
        opening_wizard_completed: true,
        opening_wizard_completed_at: new Date().toISOString(),
      };

      // Update metadata
      const { error: updateError } = await supabase
        .from("case_files")
        .update({ metadata: updatedMetadata })
        .eq("id", caseFileId)
        .eq("organization_id", organization.id);

      if (updateError) {
        console.error("Error updating wizard status:", updateError);
        return false;
      }

      return true;
    } catch (error) {
      console.error("Error marking wizard complete:", error);
      return false;
    }
  }
}

// Export convenience functions
export const checkWizardStatus = WizardService.checkWizardStatus;
export const markWizardComplete = WizardService.markWizardComplete;
