import { createClient } from "@/lib/supabase/server";
import { ProfileService } from "@/app/[lang]/protected/organization/(features)/profile/lib/services/ProfileService";

export interface DocumentWithSignature {
  id: string;
  document_name: string;
  document_type: string;
  file_path: string;
  file_size: number | null;
  attached_to_type: string;
  attached_to_id: string;
  contact_id: string | null;
  template_id: string | null;
  attachment_type: string | null;
  status: string | null;
  metadata: any;
  uploaded_at: string | null;
  // Signature information
  signature_required: boolean;
  signature_status: string | null; // 'pending', 'signed', 'rejected', 'expired'
  signed_at: string | null;
}

/**
 * Service for fetching documents related to case files and contacts
 */
export class DocumentService {
  /**
   * Get documents for a specific case file
   * @param caseFileId The case file ID
   * @returns Promise<DocumentWithSignature[]>
   */
  static async getDocumentsForCaseFile(caseFileId: string): Promise<DocumentWithSignature[]> {
    try {
      const organization = await ProfileService.getCurrentOrganization();
      if (!organization) {
        return [];
      }

      const supabase = await createClient();

      const { data, error } = await supabase
        .from("document_attachments")
        .select(
          `
          id,
          document_name,
          document_type,
          file_path,
          file_size,
          attached_to_type,
          attached_to_id,
          contact_id,
          template_id,
          attachment_type,
          status,
          metadata,
          uploaded_at,
          document_templates!document_attachments_template_id_fkey(
            signature_required
          ),
          document_signatures(
            signature_status,
            signed_at
          )
        `
        )
        .eq("organization_id", organization.id)
        .eq("attached_to_type", "case_file")
        .eq("attached_to_id", caseFileId)
        .eq("status", "attached")
        .order("uploaded_at", { ascending: false });

      if (error) {
        console.error("Error fetching case file documents:", error);
        return [];
      }

      return (data || []).map((doc) => ({
        id: doc.id,
        document_name: doc.document_name,
        document_type: doc.document_type,
        file_path: doc.file_path,
        file_size: doc.file_size,
        attached_to_type: doc.attached_to_type,
        attached_to_id: doc.attached_to_id,
        contact_id: doc.contact_id,
        template_id: doc.template_id,
        attachment_type: doc.attachment_type,
        status: doc.status,
        metadata: doc.metadata,
        uploaded_at: doc.uploaded_at,
        signature_required: doc.document_templates?.signature_required || false,
        signature_status: doc.document_signatures?.[0]?.signature_status || null,
        signed_at: doc.document_signatures?.[0]?.signed_at || null,
      }));
    } catch (error) {
      console.error("Error in getDocumentsForCaseFile:", error);
      return [];
    }
  }

  /**
   * Get documents for a specific contact within a case file
   * @param caseFileId The case file ID
   * @param contactId The contact ID
   * @returns Promise<DocumentWithSignature[]>
   */
  static async getDocumentsForContact(
    caseFileId: string,
    contactId: string
  ): Promise<DocumentWithSignature[]> {
    try {
      const organization = await ProfileService.getCurrentOrganization();
      if (!organization) {
        return [];
      }

      const supabase = await createClient();

      const { data, error } = await supabase
        .from("document_attachments")
        .select(
          `
          id,
          document_name,
          document_type,
          file_path,
          file_size,
          attached_to_type,
          attached_to_id,
          contact_id,
          template_id,
          attachment_type,
          status,
          metadata,
          uploaded_at,
          document_templates!document_attachments_template_id_fkey(
            signature_required
          ),
          document_signatures!document_signatures_document_id_fkey(
            signature_status,
            signed_at,
            contact_id
          )
        `
        )
        .eq("organization_id", organization.id)
        .or(
          `and(attached_to_type.eq.case_file,attached_to_id.eq.${caseFileId}),and(attached_to_type.eq.contact,attached_to_id.eq.${contactId})`
        )
        .eq("status", "attached")
        .order("uploaded_at", { ascending: false });

      if (error) {
        console.error("Error fetching contact documents:", error);
        return [];
      }

      return (data || []).map((doc) => {
        // Find signature for this specific contact
        const contactSignature = doc.document_signatures?.find(
          (sig: any) => sig.contact_id === contactId
        );

        return {
          id: doc.id,
          document_name: doc.document_name,
          document_type: doc.document_type,
          file_path: doc.file_path,
          file_size: doc.file_size,
          attached_to_type: doc.attached_to_type,
          attached_to_id: doc.attached_to_id,
          contact_id: doc.contact_id,
          template_id: doc.template_id,
          attachment_type: doc.attachment_type,
          status: doc.status,
          metadata: doc.metadata,
          uploaded_at: doc.uploaded_at,
          signature_required: doc.document_templates?.signature_required || false,
          signature_status: contactSignature?.signature_status || null,
          signed_at: contactSignature?.signed_at || null,
        };
      });
    } catch (error) {
      console.error("Error in getDocumentsForContact:", error);
      return [];
    }
  }

  /**
   * Get the status for a document based on signature requirements and status
   * @param document The document with signature information
   * @returns Document status string
   */
  static getDocumentStatus(document: DocumentWithSignature): string {
    if (!document.signature_required) {
      return "completed";
    }

    switch (document.signature_status) {
      case "signed":
        return "completed";
      case "pending":
        return "pending_signature";
      case "rejected":
        return "rejected";
      case "expired":
        return "expired";
      default:
        return "pending_signature";
    }
  }

  /**
   * Check if a document requires action from the contact
   * @param document The document with signature information
   * @returns True if action is required
   */
  static requiresAction(document: DocumentWithSignature): boolean {
    if (!document.signature_required) {
      return false;
    }

    return document.signature_status === null || document.signature_status === "pending";
  }
}
