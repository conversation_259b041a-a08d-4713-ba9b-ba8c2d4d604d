"use client";

import { Badge } from "@/components/ui/badge";
import { H1, P, Lead } from "@/components/typography";
import { FileText, Calendar, User, Hash } from "lucide-react";
import { CaseFileWithRelations } from "../../../lib/types";

interface EnhancedPageHeaderProps {
  caseFile: CaseFileWithRelations;
}

/**
 * Enhanced page header showing comprehensive case file information
 * Displays service details, reference number, status, and request information
 */
export function EnhancedPageHeader({ caseFile }: EnhancedPageHeaderProps) {
  const getStatusBadge = () => {
    return (
      <Badge variant="secondary" className="flex items-center gap-1">
        <FileText className="h-3 w-3" />
        Opening
      </Badge>
    );
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "long",
      day: "numeric",
    });
  };

  return (
    <div className="space-y-4">
      {/* Main Title and Status */}
      <div className="flex items-start justify-between">
        <div className="space-y-2">
          <div className="flex items-center gap-3">
            <H1>Case File {caseFile.case_number}</H1>
            {getStatusBadge()}
          </div>
          
          {caseFile.request?.service?.name && (
            <Lead>{caseFile.request.service.name}</Lead>
          )}
        </div>
      </div>

      {/* Request Information Grid */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        {/* Reference Number */}
        {caseFile.request?.reference_number && (
          <div className="flex items-center gap-2">
            <Hash className="h-4 w-4 text-muted-foreground" />
            <div>
              <P className="text-sm font-medium">Reference</P>
              <P className="text-sm">{caseFile.request.reference_number}</P>
            </div>
          </div>
        )}

        {/* Created Date */}
        <div className="flex items-center gap-2">
          <Calendar className="h-4 w-4 text-muted-foreground" />
          <div>
            <P className="text-sm font-medium">Created</P>
            <P className="text-sm">{formatDate(caseFile.created_at)}</P>
          </div>
        </div>

        {/* Assigned To */}
        {caseFile.employees && (
          <div className="flex items-center gap-2">
            <User className="h-4 w-4 text-muted-foreground" />
            <div>
              <P className="text-sm font-medium">Assigned To</P>
              <P className="text-sm">
                {caseFile.employees.first_name} {caseFile.employees.last_name}
              </P>
            </div>
          </div>
        )}
      </div>

      {/* Request Description */}
      {caseFile.request?.description && (
        <div className="border-l-4 border-blue-200 pl-4">
          <P className="text-sm font-medium mb-1">Request Details</P>
          <P className="text-sm text-muted-foreground">
            {caseFile.request.description}
          </P>
        </div>
      )}
    </div>
  );
}
