"use client";

import { motion } from "framer-motion";
import Link from "next/link";
import { ChevronRight, Check } from "lucide-react";
import { P } from "@/components/typography";
import { Dictionary } from "@/lib/i18n/services/I18nService";

interface WizardBreadcrumbProps {
  currentStep: "welcome" | "contacts" | "documents" | "complete";
  caseFileId: string;
  lang: string;
  dictionary: Dictionary;
}

/**
 * Wizard breadcrumb navigation
 * Shows progress and allows navigation between completed steps
 */
export function WizardBreadcrumb({ currentStep, caseFileId, lang, dictionary }: WizardBreadcrumbProps) {
  const steps = [
    {
      key: "welcome",
      label: dictionary.caseFileOpening.wizard.breadcrumb.welcome,
      href: `/${lang}/protected/case-file/${caseFileId}/opening/welcome`,
    },
    {
      key: "contacts",
      label: dictionary.caseFileOpening.wizard.breadcrumb.contacts,
      href: `/${lang}/protected/case-file/${caseFileId}/opening/contacts`,
    },
    {
      key: "documents",
      label: dictionary.caseFileOpening.wizard.breadcrumb.documents,
      href: `/${lang}/protected/case-file/${caseFileId}/opening/documents`,
    },
    {
      key: "complete",
      label: dictionary.caseFileOpening.wizard.breadcrumb.complete,
      href: `/${lang}/protected/case-file/${caseFileId}/opening`,
    },
  ];

  const getCurrentStepIndex = () => {
    return steps.findIndex((step) => step.key === currentStep);
  };

  const currentIndex = getCurrentStepIndex();

  const getStepStatus = (index: number) => {
    if (index < currentIndex) return "completed";
    if (index === currentIndex) return "current";
    return "upcoming";
  };

  return (
    <motion.nav
      initial={{ opacity: 0, y: -20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.6, ease: "easeOut" }}
      className="flex items-center space-x-2 mb-6 p-4 bg-white rounded-lg border border-gray-200 shadow-sm"
    >
      {steps.map((step, index) => {
        const status = getStepStatus(index);
        const isClickable = status === "completed" || status === "current";

        return (
          <div key={step.key} className="flex items-center">
            {/* Step */}
            <motion.div
              initial={{ scale: 0.8, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              transition={{ delay: index * 0.1, duration: 0.3 }}
              className="flex items-center"
            >
              {isClickable ? (
                <Link href={step.href} className="flex items-center group">
                  <div
                    className={`
                      flex items-center justify-center w-8 h-8 rounded-full border-2 transition-all duration-300
                      ${
                        status === "completed"
                          ? "bg-green-500 border-green-500 text-white"
                          : status === "current"
                            ? "bg-blue-500 border-blue-500 text-white"
                            : "bg-gray-100 border-gray-300 text-gray-500"
                      }
                      ${isClickable ? "group-hover:scale-110" : ""}
                    `}
                  >
                    {status === "completed" ? (
                      <Check className="h-4 w-4" />
                    ) : (
                      <span className="text-sm font-medium">{index + 1}</span>
                    )}
                  </div>
                  <P
                    className={`
                      ml-2 text-sm font-medium transition-colors duration-300
                      ${
                        status === "current"
                          ? "text-blue-600"
                          : status === "completed"
                            ? "text-green-600 group-hover:text-green-700"
                            : "text-gray-500"
                      }
                    `}
                  >
                    {step.label}
                  </P>
                </Link>
              ) : (
                <div className="flex items-center">
                  <div className="flex items-center justify-center w-8 h-8 rounded-full border-2 bg-gray-100 border-gray-300 text-gray-400">
                    <span className="text-sm font-medium">{index + 1}</span>
                  </div>
                  <P className="ml-2 text-sm font-medium text-gray-400">{step.label}</P>
                </div>
              )}
            </motion.div>

            {/* Separator */}
            {index < steps.length - 1 && (
              <motion.div
                initial={{ opacity: 0, scale: 0.5 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ delay: index * 0.1 + 0.2, duration: 0.3 }}
                className="mx-3"
              >
                <ChevronRight
                  className={`
                    h-4 w-4 transition-colors duration-300
                    ${index < currentIndex ? "text-green-400" : "text-gray-300"}
                  `}
                />
              </motion.div>
            )}
          </div>
        );
      })}
    </motion.nav>
  );
}
