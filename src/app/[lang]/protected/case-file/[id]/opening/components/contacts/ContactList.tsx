"use client";

import { motion } from "framer-motion";
import { ContactCard } from "./ContactCard";

interface Contact {
  id: string;
  name: string;
  email?: any;
  phone?: any;
  relationship_type: string;
}

interface ContactListProps {
  contacts: Contact[];
  caseFileId: string;
  lang: string;
}

/**
 * Contact list component with staggered animations
 * Displays contacts as clickable cards
 */
export function ContactList({ contacts, caseFileId, lang }: ContactListProps) {
  if (!contacts || contacts.length === 0) {
    return (
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.5 }}
        className="text-center py-8"
      >
        <div className="text-muted-foreground">
          <p className="text-sm">No contacts found</p>
          <p className="text-xs mt-1">Add contacts to get started</p>
        </div>
      </motion.div>
    );
  }

  return (
    <div className="space-y-3">
      {contacts.map((contact, index) => (
        <motion.div
          key={contact.id}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{
            delay: index * 0.1,
            duration: 0.5,
            ease: "easeOut",
          }}
        >
          <ContactCard contact={contact} caseFileId={caseFileId} lang={lang} />
        </motion.div>
      ))}
    </div>
  );
}
