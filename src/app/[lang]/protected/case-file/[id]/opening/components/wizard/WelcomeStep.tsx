"use client";

import { motion } from "framer-motion";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { H2, P } from "@/components/typography";
import { FileText, Users, CheckCircle, ArrowRight } from "lucide-react";
import Link from "next/link";

interface WelcomeStepProps {
  caseFileId: string;
  lang: string;
}

/**
 * Welcome step component for the opening case file wizard
 * Introduces the document workflow and provides call-to-action
 */
export function WelcomeStep({ caseFileId, lang }: WelcomeStepProps) {
  return (
    <motion.div
      initial={{ opacity: 0, y: 30 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 1.2, ease: [0.25, 0.46, 0.45, 0.94] }}
      className="max-w-2xl mx-auto"
    >
      <Card className="text-center">
        <CardContent className="space-y-6 p-8">
          {/* Icon */}
          <motion.div
            initial={{ scale: 0 }}
            animate={{ scale: 1 }}
            transition={{ delay: 0.6, duration: 0.8, type: "spring", stiffness: 100 }}
            className="flex justify-center"
          >
            <div className="bg-blue-50 p-4 rounded-full">
              <FileText className="h-12 w-12 text-blue-600" />
            </div>
          </motion.div>

          {/* Title */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 1.0, duration: 0.8, ease: "easeOut" }}
          >
            <H2>Welcome to Document Processing</H2>
          </motion.div>

          {/* Description */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 1.4, duration: 0.8, ease: "easeOut" }}
            className="space-y-4"
          >
            <P>
              You're here to complete document signatures and acknowledgments for this case file.
              This workflow will help you manage all required documents efficiently.
            </P>

            {/* Features List */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-left">
              <motion.div
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: 0.9, duration: 0.5 }}
                className="flex items-start gap-3"
              >
                <Users className="h-5 w-5 text-blue-600 mt-1" />
                <div>
                  <P className="font-medium">Contact Management</P>
                  <P className="text-sm text-muted-foreground">View and select contacts</P>
                </div>
              </motion.div>

              <motion.div
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: 1.1, duration: 0.5 }}
                className="flex items-start gap-3"
              >
                <FileText className="h-5 w-5 text-blue-600 mt-1" />
                <div>
                  <P className="font-medium">Document Review</P>
                  <P className="text-sm text-muted-foreground">View and manage documents</P>
                </div>
              </motion.div>

              <motion.div
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: 1.3, duration: 0.5 }}
                className="flex items-start gap-3"
              >
                <CheckCircle className="h-5 w-5 text-blue-600 mt-1" />
                <div>
                  <P className="font-medium">Signature & Approval</P>
                  <P className="text-sm text-muted-foreground">Complete required actions</P>
                </div>
              </motion.div>
            </div>
          </motion.div>

          {/* Call to Action */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 1.5, duration: 0.5 }}
          >
            <Link href={`/${lang}/protected/case-file/${caseFileId}/opening/contacts`}>
              <Button size="lg" className="group">
                Get Started
                <ArrowRight className="ml-2 h-4 w-4 transition-transform group-hover:translate-x-1" />
              </Button>
            </Link>
          </motion.div>
        </CardContent>
      </Card>
    </motion.div>
  );
}
