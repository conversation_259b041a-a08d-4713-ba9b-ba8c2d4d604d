"use client";

import { motion } from "framer-motion";
import { Card, CardContent } from "@/components/ui/card";
import { H2, P } from "@/components/typography";
import { ContactList } from "../contacts/ContactList";
import { EmptyDocumentState } from "../documents/EmptyDocumentState";
import { CaseFileWithRelations } from "../../../../lib/types";

interface ContactsRevealStepProps {
  caseFile: CaseFileWithRelations;
  lang: string;
}

/**
 * Contacts reveal step component
 * Shows the contact list with fade-in animation and empty document state
 */
export function ContactsRevealStep({ caseFile, lang }: ContactsRevealStepProps) {
  return (
    <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
      {/* Left Sidebar - Contact List */}
      <motion.div
        initial={{ opacity: 0, x: -50 }}
        animate={{ opacity: 1, x: 0 }}
        transition={{ duration: 0.8, ease: "easeOut" }}
        className="lg:col-span-1"
      >
        <Card>
          <CardContent className="p-6">
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 0.3, duration: 0.5 }}
              className="space-y-4"
            >
              <H2>Contacts</H2>
              <P className="text-sm text-muted-foreground">
                Click on a contact to view their documents
              </P>

              <ContactList
                contacts={caseFile.contacts || []}
                caseFileId={caseFile.id}
                lang={lang}
              />
            </motion.div>
          </CardContent>
        </Card>
      </motion.div>

      {/* Right Main Content - Empty State */}
      <motion.div
        initial={{ opacity: 0, x: 50 }}
        animate={{ opacity: 1, x: 0 }}
        transition={{ delay: 0.4, duration: 0.8, ease: "easeOut" }}
        className="lg:col-span-2"
      >
        <EmptyDocumentState />
      </motion.div>
    </div>
  );
}
