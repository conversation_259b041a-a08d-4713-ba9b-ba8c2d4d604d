"use client";

import { motion } from "framer-motion";
import { Card, CardContent } from "@/components/ui/card";
import { H2, P } from "@/components/typography";
import { FileText, ArrowLeft } from "lucide-react";

/**
 * Empty document state component
 * Shown when no contact is selected
 */
export function EmptyDocumentState() {
  return (
    <Card className="h-full">
      <CardContent className="p-8">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, ease: "easeOut" }}
          className="text-center space-y-6"
        >
          {/* Icon */}
          <motion.div
            initial={{ scale: 0 }}
            animate={{ scale: 1 }}
            transition={{ delay: 0.2, type: "spring", stiffness: 200 }}
            className="flex justify-center"
          >
            <div className="bg-gray-50 p-6 rounded-full">
              <FileText className="h-16 w-16 text-gray-400" />
            </div>
          </motion.div>

          {/* Title */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.4, duration: 0.5 }}
          >
            <H2>Document Review & Signature Workflow</H2>
          </motion.div>

          {/* Description */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.6, duration: 0.5 }}
            className="space-y-4"
          >
            <P className="text-muted-foreground">
              This workflow is for document reviews, signatures, and acknowledgments.
            </P>

            <div className="flex items-center justify-center gap-2 text-sm text-muted-foreground">
              <ArrowLeft className="h-4 w-4" />
              <span>
                Please click on a contact from the list to view their documents and take required
                actions.
              </span>
            </div>
          </motion.div>

          {/* Features */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.8, duration: 0.5 }}
            className="grid grid-cols-1 md:grid-cols-2 gap-4 text-left max-w-md mx-auto"
          >
            <div className="space-y-2">
              <div className="flex items-center gap-2">
                <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                <P className="text-sm font-medium">View Documents</P>
              </div>
              <div className="flex items-center gap-2">
                <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                <P className="text-sm font-medium">Digital Signatures</P>
              </div>
            </div>
            <div className="space-y-2">
              <div className="flex items-center gap-2">
                <div className="w-2 h-2 bg-purple-500 rounded-full"></div>
                <P className="text-sm font-medium">Acknowledgments</P>
              </div>
              <div className="flex items-center gap-2">
                <div className="w-2 h-2 bg-orange-500 rounded-full"></div>
                <P className="text-sm font-medium">Progress Tracking</P>
              </div>
            </div>
          </motion.div>
        </motion.div>
      </CardContent>
    </Card>
  );
}
