import { notFound, redirect } from "next/navigation";
import { getCaseFileWithRelations } from "../../../actions";
import { markWizardComplete } from "../lib/wizardService";

interface DocumentsPageProps {
  params: Promise<{
    lang: string;
    id: string;
  }>;
}

/**
 * Documents reveal step of the opening case file wizard
 * Third step that shows the complete interface with documents
 */
export default async function DocumentsPage({ params }: DocumentsPageProps) {
  const { lang, id } = await params;

  // Get case file data
  const caseFileResponse = await getCaseFileWithRelations(id);

  if (!caseFileResponse.success || !caseFileResponse.data) {
    notFound();
  }

  const caseFile = caseFileResponse.data;

  // Mark wizard as complete and redirect to main page
  await markWizardComplete(id);
  redirect(`/${lang}/protected/case-file/${id}/opening`);

  return null;
}
