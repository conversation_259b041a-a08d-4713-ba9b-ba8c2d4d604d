import { notFound, redirect } from "next/navigation";
import { getCaseFileWithRelations } from "../../actions";
import { checkWizardStatus, markWizardComplete } from "./lib/wizardService";
import { EnhancedPageHeader } from "./components/EnhancedPageHeader";
import { DocumentWorkflow } from "./components/documents/DocumentWorkflow";
import { ContactList } from "./components/contacts/ContactList";
import { Card, CardContent } from "@/components/ui/card";
import { H2, P } from "@/components/typography";

interface OpeningCaseFilePageProps {
  params: Promise<{
    lang: string;
    id: string;
  }>;
}

/**
 * Opening Case File Page
 * Displays case files in the opening state with setup checklist
 */
export default async function OpeningCaseFilePage({ params }: OpeningCaseFilePageProps) {
  const { lang, id } = await params;

  // Get case file data
  const caseFileResponse = await getCaseFileWithRelations(id);

  if (!caseFileResponse.success || !caseFileResponse.data) {
    notFound();
  }

  const caseFile = caseFileResponse.data;

  // Opening checklist items
  const checklistItems = [
    {
      id: "initial_assessment",
      title: "Initial Assessment Completed",
      description: "Complete initial family assessment and needs evaluation",
      completed: false,
      required: true,
    },
    {
      id: "documentation",
      title: "Required Documentation Collected",
      description: "Gather all necessary documents and forms",
      completed: false,
      required: true,
    },
    {
      id: "service_plan",
      title: "Service Plan Developed",
      description: "Create comprehensive service plan with family",
      completed: false,
      required: true,
    },
    {
      id: "contacts_added",
      title: "Family Contacts Added",
      description: "Add all family members and emergency contacts",
      completed: caseFile.contacts && caseFile.contacts.length > 0,
      required: true,
    },
    {
      id: "case_worker_assigned",
      title: "Case Worker Assigned",
      description: "Assign primary case worker to the family",
      completed: !!caseFile.assigned_to,
      required: true,
    },
  ];

  const completedItems = checklistItems.filter((item) => item.completed).length;
  const totalItems = checklistItems.length;
  const canActivate = checklistItems
    .filter((item) => item.required)
    .every((item) => item.completed);

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <PageHeader
        title={`Case File ${caseFile.case_number} - Opening`}
        description="Complete setup requirements to activate this case file"
        actions={
          <div className="flex gap-2">
            <Button variant="outline">
              <Settings className="mr-2" />
              Settings
            </Button>
            <Button disabled={!canActivate}>
              <Play className="mr-2" />
              Activate Case
            </Button>
          </div>
        }
      />

      {/* Case File Navigation */}
      <CaseFileNavigation caseFileId={caseFile.id} currentStatus={caseFile.status} lang={lang} />

      {/* Opening Progress */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Clock className="mr-2" />
            Opening Progress
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div>
            <div className="flex justify-between items-center">
              <P>Setup Progress</P>
              <P>
                {completedItems}/{totalItems} completed
              </P>
            </div>
          </div>

          {canActivate && (
            <div>
              <div className="flex items-center">
                <CheckCircle className="mr-2" />
                <P>Ready to activate! All required items completed.</P>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Setup Checklist */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <FileText className="mr-2" />
            Setup Checklist
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {checklistItems.map((item) => (
              <div key={item.id} className="flex items-start space-x-3">
                <div>{item.completed ? <CheckCircle /> : <div />}</div>
                <div>
                  <H2>
                    {item.title}
                    {item.required && <span>*</span>}
                  </H2>
                  <P>{item.description}</P>
                </div>
                <div>
                  <Button
                    variant={item.completed ? "outline" : "default"}
                    disabled={item.completed}
                  >
                    {item.completed ? "Completed" : "Start"}
                  </Button>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Family Information */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Users className="mr-2" />
            Family Information
          </CardTitle>
        </CardHeader>
        <CardContent>
          {caseFile.contacts && caseFile.contacts.length > 0 ? (
            <div>
              {caseFile.contacts.map((contact) => (
                <div key={contact.id} className="flex items-center justify-between">
                  <div>
                    <P>{contact.name}</P>
                    <P>
                      {contact.email || "No email"} • {contact.phone || "No phone"}
                    </P>
                  </div>
                  <Button variant="outline">Edit</Button>
                </div>
              ))}
            </div>
          ) : (
            <div>
              <Users />
              <P>No family contacts added yet</P>
              <Button>Add Family Contact</Button>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
