import { notFound } from "next/navigation";
import { getCaseFileWithRelations } from "../../actions";
import { H2, P } from "@/components/typography";

interface OpeningCaseFilePageProps {
  params: Promise<{
    lang: string;
    id: string;
  }>;
}

/**
 * Main Opening Case File Page
 * Simple implementation for now
 */
export default async function OpeningCaseFilePage({ params }: OpeningCaseFilePageProps) {
  const { lang: _lang, id } = await params;

  // Get case file data
  const caseFileResponse = await getCaseFileWithRelations(id);

  if (!caseFileResponse.success || !caseFileResponse.data) {
    notFound();
  }

  const caseFile = caseFileResponse.data;

  return (
    <div className="space-y-6">
      <H2>Case File {caseFile.case_number} - Opening</H2>
      <P>Document workflow interface will be implemented here.</P>
      <P>Contacts: {caseFile.contacts?.length || 0}</P>
    </div>
  );
}
