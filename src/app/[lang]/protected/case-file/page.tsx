import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { H1, P } from "@/components/typography";
import { Plus, Search, FileText } from "lucide-react";
import Link from "next/link";
import { getCaseFileStatistics, getRecentCaseFiles } from "./actions";
import { CaseFileStats, CaseFileCard } from "./components";

interface CaseFilePageProps {
  params: Promise<{
    lang: string;
  }>;
}

/**
 * Case File Domain Index Page
 * Lists all case files and provides navigation to different states
 */
export default async function CaseFilePage({ params }: CaseFilePageProps) {
  const { lang } = await params;

  // Get statistics and recent case files
  const [statisticsResponse, recentCaseFilesResponse] = await Promise.all([
    getCaseFileStatistics(),
    getRecentCaseFiles(10),
  ]);

  // Use fallback data if requests fail
  const statistics =
    statisticsResponse.success && statisticsResponse.data
      ? statisticsResponse.data
      : {
          opening: 0,
          active: 0,
          suspended: 0,
          closed: 0,
          total: 0,
        };

  const recentCaseFiles =
    recentCaseFilesResponse.success && recentCaseFilesResponse.data
      ? recentCaseFilesResponse.data.map((caseFile) => ({
          id: caseFile.id,
          case_number: caseFile.case_number,
          status: caseFile.status,
          created_at: caseFile.created_at,
          activated_at: caseFile.activated_at,
          suspended_at: caseFile.suspended_at,
          closed_at: caseFile.closed_at,
          assigned_to: caseFile.assigned_to,
          primaryContactName: undefined,
          totalContacts: 0,
          upcomingAppointments: 0,
          pendingDocuments: 0,
        }))
      : [];

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
        <div className="flex items-center">
          <div className="bg-primary/10 p-2 rounded-full mr-3">
            <FileText className="h-10 w-10 text-primary" />
          </div>
          <div>
            <H1>Case Files</H1>
            <P className="mt-1 text-muted-foreground">
              Manage family service case files and track progress
            </P>
          </div>
        </div>

        <div className="flex items-center gap-4">
          <Button variant="outline" className="flex items-center gap-2" asChild>
            <Link href={`/${lang}/protected/case-file/list`}>
              <Search className="h-4 w-4" />
              Search Cases
            </Link>
          </Button>
          <Button className="flex items-center gap-2" asChild>
            <Link href={`/${lang}/protected/case-file/create`}>
              <Plus className="h-4 w-4" />
              New Case File
            </Link>
          </Button>
        </div>
      </div>

      {/* Quick Stats */}
      <CaseFileStats statistics={statistics} />

      {/* Case Files List */}
      <Card>
        <CardHeader>
          <CardTitle>Recent Case Files</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {recentCaseFiles && recentCaseFiles.length > 0 ? (
              recentCaseFiles.map((caseFile) => (
                <CaseFileCard key={caseFile.id} caseFile={caseFile} lang={lang} />
              ))
            ) : (
              <div className="text-center py-8 text-muted-foreground">
                <P className="text-sm">No case files found</P>
              </div>
            )}
          </div>

          <div className="mt-6">
            <Button variant="outline" className="w-full" asChild>
              <Link href={`/${lang}/protected/case-file/list`}>View All Case Files</Link>
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
