import type { EmployeeRow } from "../shared/types/database";

/**
 * Employee domain types
 */

/**
 * Employee with extracted email for UI display
 */
export interface EmployeeWithEmail {
  id: string;
  firstName: string;
  lastName: string;
  email?: string;
  jobTitle?: string;
  department?: string;
}

/**
 * Employee summary for lists and cards
 */
export interface EmployeeSummary {
  id: string;
  firstName: string;
  lastName: string;
  email?: string;
  jobTitle?: string;
  employmentStatus: string;
}

/**
 * Employee with full contact information
 */
export interface EmployeeWithContacts extends EmployeeWithEmail {
  phone?: string;
  address?: any;
  employmentStatus: string;
  hireDate?: string;
  supervisor?: EmployeeSummary;
}

/**
 * Employee query parameters
 */
export interface EmployeeQueryParams {
  includeEmail?: boolean;
  includePhone?: boolean;
  includeAddress?: boolean;
  includeSupervisor?: boolean;
}

/**
 * Employee filters
 */
export interface EmployeeFilters {
  employmentStatus?: string | string[];
  department?: string;
  jobTitle?: string;
  supervisorId?: string;
  organizationId?: string;
}
