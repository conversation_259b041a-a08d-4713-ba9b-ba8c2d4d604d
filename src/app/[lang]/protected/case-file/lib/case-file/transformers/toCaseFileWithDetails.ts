import type { CaseFileRow, EmployeeRow } from "../../shared/types/database";
import type { CaseFileWithDetails, CaseFileTransformContext } from "../types";
import type { ContactWithRelationship } from "../../contact/types";
import { toEmployeeWithEmail } from "../../employee/transformers/toEmployeeWithEmail";
import { toCaseFileContactInfoArray } from "../../contact/transformers/toContactInfo";

/**
 * Transform case file data to case file with details
 * @param context Transformation context with case file and related data
 * @returns Case file with details
 */
export const toCaseFileWithDetails = (context: {
  caseFile: CaseFileRow;
  employee?: EmployeeRow | null;
  contacts: ContactWithRelationship[];
}): CaseFileWithDetails => {
  const { caseFile, employee, contacts } = context;

  return {
    id: caseFile.id,
    caseNumber: caseFile.case_number,
    status: caseFile.status as any, // Type assertion for enum
    organizationId: caseFile.organization_id,
    requestId: caseFile.request_id || undefined,
    createdAt: caseFile.created_at || "",
    updatedAt: caseFile.updated_at || "",
    openedAt: caseFile.opened_at || undefined,
    activatedAt: caseFile.activated_at || undefined,
    suspendedAt: caseFile.suspended_at || undefined,
    closedAt: caseFile.closed_at || undefined,
    assignedTo: caseFile.assigned_to || undefined,
    createdBy: caseFile.created_by || undefined,
    employee: employee ? toEmployeeWithEmail(employee) : undefined,
    contacts: toCaseFileContactInfoArray(contacts, caseFile.id),
    metadata: caseFile.metadata,
  };
};

/**
 * Transform case file row to basic case file info
 * @param caseFile Raw case file data
 * @returns Basic case file info
 */
export const toCaseFileBasic = (caseFile: CaseFileRow) => ({
  id: caseFile.id,
  caseNumber: caseFile.case_number,
  status: caseFile.status as any,
  createdAt: caseFile.created_at || "",
  assignedTo: caseFile.assigned_to || undefined,
});

/**
 * Transform array of case file rows to basic case file info array
 * @param caseFiles Array of raw case file data
 * @returns Array of basic case file info
 */
export const toCaseFileBasicArray = (caseFiles: CaseFileRow[]) => caseFiles.map(toCaseFileBasic);
