import type { CaseFileRow, CaseFileStatus } from "../shared/types/database";
import type { EmployeeWithEmail } from "../employee/types";
import type { CaseFileContactInfo } from "../contact/types";

/**
 * Case file domain types
 */

/**
 * Case file with full details for display
 */
export interface CaseFileWithDetails {
  id: string;
  caseNumber: string;
  status: CaseFileStatus;
  organizationId: string;
  requestId?: string;
  createdAt: string;
  updatedAt: string;
  openedAt?: string;
  activatedAt?: string;
  suspendedAt?: string;
  closedAt?: string;
  assignedTo?: string;
  createdBy?: string;
  employee?: EmployeeWithEmail;
  contacts: CaseFileContactInfo[];
  metadata?: any;
}

/**
 * Case file summary for lists and cards
 */
export interface CaseFileSummary {
  id: string;
  caseNumber: string;
  status: CaseFileStatus;
  createdAt: string;
  activatedAt?: string;
  suspendedAt?: string;
  closedAt?: string;
  assignedTo?: string;
  primaryContactName?: string;
  totalContacts: number;
  upcomingAppointments: number;
  pendingDocuments: number;
}

/**
 * Case file basic info
 */
export interface CaseFileBasic {
  id: string;
  caseNumber: string;
  status: CaseFileStatus;
  createdAt: string;
  assignedTo?: string;
}

/**
 * Case file with request information
 */
export interface CaseFileWithRequest extends CaseFileWithDetails {
  request?: {
    id: string;
    referenceNumber?: string;
    title?: string;
    description?: string;
    service?: {
      id: string;
      name: string;
      description?: string;
    };
  };
}

/**
 * Case file query parameters
 */
export interface CaseFileQueryParams {
  includeEmployee?: boolean;
  includeContacts?: boolean;
  includeRequest?: boolean;
  includeHistory?: boolean;
}

/**
 * Case file transformation context
 */
export interface CaseFileTransformContext {
  employee?: EmployeeWithEmail;
  contacts: CaseFileContactInfo[];
  request?: any;
  history?: any[];
}
