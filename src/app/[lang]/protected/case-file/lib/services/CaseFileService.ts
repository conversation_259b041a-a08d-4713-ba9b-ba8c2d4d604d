import { createClient } from "@/lib/supabase/server";
import { logger } from "@/lib/logger/services/LoggerService";
import {
  CaseFile,
  CaseFileInsert,
  CaseFileUpdate,
  CaseFileWithRelations,
  CaseFileListParams,
  CaseFileSummary,
  CaseFileStatus,
} from "../types";
import { ServiceResponse, successResponse, errorResponse } from "@/lib/types/responses";
import { ProfileService } from "@/app/[lang]/protected/organization/(features)/profile/lib/services/ProfileService";

/**
 * Service for case file management operations
 * Follows server-first paradigm with static methods
 */
export class CaseFileService {
  /**
   * Helper method to extract primary email from employee emails JSONB array
   * @param emails The emails JSONB array from employee record
   * @returns Primary email or undefined
   */
  private static extractPrimaryEmail(emails: any): string | undefined {
    if (!emails || !Array.isArray(emails)) return undefined;

    // Find primary email first
    const primaryEmail = emails.find((email: any) => email.primary === true);
    if (primaryEmail?.email) return primaryEmail.email;

    // Fall back to work email
    const workEmail = emails.find((email: any) => email.type === "work");
    if (workEmail?.email) return workEmail.email;

    // Fall back to first email
    if (emails.length > 0 && emails[0]?.email) return emails[0].email;

    return undefined;
  }

  /**
   * Helper method to extract email from contact email JSONB object
   * @param emailData The email JSONB object from contact record
   * @returns Primary email or undefined
   */
  private static extractContactEmail(emailData: any): string | undefined {
    if (!emailData) return undefined;

    // Handle different email data structures
    if (typeof emailData === "string") return emailData;
    if (typeof emailData === "object") {
      return emailData.personal || emailData.work || emailData.main || emailData.primary;
    }

    return undefined;
  }

  /**
   * Helper method to extract phone from contact phone JSONB object
   * @param phoneData The phone JSONB object from contact record
   * @returns Primary phone or undefined
   */
  private static extractContactPhone(phoneData: any): string | undefined {
    if (!phoneData) return undefined;

    // Handle different phone data structures
    if (typeof phoneData === "string") return phoneData;
    if (typeof phoneData === "object") {
      return phoneData.mobile || phoneData.home || phoneData.work || phoneData.main || phoneData.primary;
    }

    return undefined;
  }

  /**
   * Generate mock case files for development
   * TODO: Remove this when real database integration is complete
   */
  private static generateMockCaseFiles(): CaseFile[] {
    const mockCaseFiles: CaseFile[] = [
      {
        id: "5ba6bbe8-0dfa-4ed0-932e-4d9a81d39317",
        case_number: "CF-2024-001",
        status: "active" as CaseFileStatus,
        organization_id: "mock-org-id",
        request_id: "mock-request-id-1",
        created_at: "2024-01-15T10:00:00Z",
        updated_at: "2024-01-20T14:30:00Z",
        opened_at: "2024-01-15T10:00:00Z",
        activated_at: "2024-01-16T09:00:00Z",
        suspended_at: null,
        closed_at: null,
        assigned_to: "John Smith",
        created_by: "admin-user-id",
        metadata: null,
      },
      {
        id: "7c8d9e0f-1a2b-3c4d-5e6f-7890abcdef12",
        case_number: "CF-2024-002",
        status: "opening" as CaseFileStatus,
        organization_id: "mock-org-id",
        request_id: "mock-request-id-2",
        created_at: "2024-01-20T11:00:00Z",
        updated_at: "2024-01-20T11:00:00Z",
        opened_at: "2024-01-20T11:00:00Z",
        activated_at: null,
        suspended_at: null,
        closed_at: null,
        assigned_to: null,
        created_by: "admin-user-id",
        metadata: null,
      },
      {
        id: "8d9e0f1a-2b3c-4d5e-6f78-90abcdef1234",
        case_number: "CF-2024-003",
        status: "suspended" as CaseFileStatus,
        organization_id: "mock-org-id",
        request_id: "mock-request-id-3",
        created_at: "2024-01-10T09:00:00Z",
        updated_at: "2024-01-18T16:00:00Z",
        opened_at: "2024-01-10T09:00:00Z",
        activated_at: "2024-01-11T10:00:00Z",
        suspended_at: "2024-01-18T16:00:00Z",
        closed_at: null,
        assigned_to: "Jane Doe",
        created_by: "admin-user-id",
        metadata: null,
      },
      {
        id: "9e0f1a2b-3c4d-5e6f-7890-abcdef123456",
        case_number: "CF-2024-004",
        status: "closed" as CaseFileStatus,
        organization_id: "mock-org-id",
        request_id: "mock-request-id-4",
        created_at: "2024-01-05T08:00:00Z",
        updated_at: "2024-01-25T17:00:00Z",
        opened_at: "2024-01-05T08:00:00Z",
        activated_at: "2024-01-06T09:00:00Z",
        suspended_at: null,
        closed_at: "2024-01-25T17:00:00Z",
        assigned_to: "Bob Wilson",
        created_by: "admin-user-id",
        metadata: null,
      },
    ];
    return mockCaseFiles;
  }
  /**
   * List case files with filtering, pagination, and sorting
   * @param params Parameters for filtering, pagination, and sorting
   * @returns Service response with the list of case files and total count
   */
  static async list(
    params: CaseFileListParams = {}
  ): Promise<ServiceResponse<{ items: CaseFile[]; total: number }>> {
    try {
      const organization = await ProfileService.getCurrentOrganization();
      if (!organization) {
        return errorResponse(null, "Organization not found");
      }
      const organizationId = organization.id;

      const {
        page = 1,
        limit = 10,
        status,
        search,
        sortBy = "created_at",
        sortOrder = "desc",
        assigned_to,
        created_by,
        from_date,
        to_date,
      } = params;

      const offset = (page - 1) * limit;

      const supabase = await createClient();

      // Query case files with related data
      let query = supabase
        .from("case_files")
        .select(
          `
          *,
          requests!case_files_request_id_fkey(
            id,
            reference_number,
            services!requests_service_id_fkey(id, name, description)
          ),
          employees!case_files_assigned_to_fkey(
            id,
            first_name,
            last_name,
            emails
          )
        `,
          {
            count: "exact",
          }
        )
        .eq("organization_id", organizationId);

      // Apply filters
      if (status) {
        if (Array.isArray(status)) {
          query = query.in("status", status);
        } else {
          query = query.eq("status", status);
        }
      }

      if (search) {
        query = query.or(`case_number.ilike.%${search}%`);
      }

      if (assigned_to) {
        query = query.eq("assigned_to", assigned_to);
      }

      if (created_by) {
        query = query.eq("created_by", created_by);
      }

      if (from_date) {
        query = query.gte("created_at", from_date);
      }

      if (to_date) {
        query = query.lte("created_at", to_date);
      }

      // Apply sorting
      query = query.order(sortBy, { ascending: sortOrder === "asc" });

      // Apply pagination
      query = query.range(offset, offset + limit - 1);

      const { data, error, count } = await query;

      if (error) {
        logger.error(`Error listing case files: ${error.message}`);
        return errorResponse(error, `Failed to list case files: ${error.message}`);
      }

      return successResponse(
        {
          items: data as CaseFile[],
          total: count || 0,
        },
        "Successfully retrieved case files"
      );
    } catch (error) {
      logger.error(`Unexpected error listing case files: ${error}`);
      return errorResponse(error, `Unexpected error listing case files`);
    }
  }

  /**
   * List case file summaries with aggregated data for list views
   * @param params Parameters for filtering, pagination, and sorting
   * @returns Service response with case file summaries and total count
   */
  static async listSummaries(
    params: CaseFileListParams = {}
  ): Promise<ServiceResponse<{ items: CaseFileSummary[]; total: number }>> {
    try {
      const organization = await ProfileService.getCurrentOrganization();
      if (!organization) {
        return errorResponse(null, "Organization not found");
      }
      const organizationId = organization.id;

      const {
        page = 1,
        limit = 10,
        status,
        search,
        sortBy = "created_at",
        sortOrder = "desc",
        assigned_to,
        created_by,
        from_date,
        to_date,
      } = params;

      const offset = (page - 1) * limit;
      const supabase = await createClient();

      // Build the main query for case files with aggregated data
      let query = supabase
        .from("case_files")
        .select(
          `
          id,
          case_number,
          status,
          created_at,
          activated_at,
          suspended_at,
          closed_at,
          assigned_to
        `,
          {
            count: "exact",
          }
        )
        .eq("organization_id", organizationId);

      // Apply filters
      if (status) {
        if (Array.isArray(status)) {
          query = query.in("status", status);
        } else {
          query = query.eq("status", status);
        }
      }

      if (search) {
        query = query.or(`case_number.ilike.%${search}%`);
      }

      if (assigned_to) {
        query = query.eq("assigned_to", assigned_to);
      }

      if (created_by) {
        query = query.eq("created_by", created_by);
      }

      if (from_date) {
        query = query.gte("created_at", from_date);
      }

      if (to_date) {
        query = query.lte("created_at", to_date);
      }

      // Apply sorting
      query = query.order(sortBy, { ascending: sortOrder === "asc" });

      // Apply pagination
      query = query.range(offset, offset + limit - 1);

      const { data: caseFilesData, error: caseFilesError, count } = await query;

      if (caseFilesError) {
        logger.error(`Error listing case file summaries: ${caseFilesError.message}`);
        return errorResponse(
          caseFilesError,
          `Failed to list case file summaries: ${caseFilesError.message}`
        );
      }

      if (!caseFilesData || caseFilesData.length === 0) {
        return successResponse({
          items: [],
          total: count || 0,
        });
      }

      // Get case file IDs for aggregation queries
      const caseFileIds = caseFilesData.map((cf) => cf.id);

      // Get contact counts and primary contacts for each case file
      const { data: contactsData } = await supabase
        .from("case_file_contacts")
        .select(
          `
          case_file_id,
          relationship_type,
          contacts!case_file_contacts_contact_id_fkey(name)
        `
        )
        .in("case_file_id", caseFileIds);

      // TODO: Add queries for appointments and documents when those features are implemented
      // For now, we'll set them to 0

      // Transform data to CaseFileSummary format
      const summaries: CaseFileSummary[] = caseFilesData.map((caseFile) => {
        // Get contacts for this case file
        const caseFileContacts = contactsData?.filter((c) => c.case_file_id === caseFile.id) || [];

        // Find primary contact (first parent/guardian or first contact)
        const primaryContact =
          caseFileContacts.find(
            (c) => c.relationship_type === "Parent" || c.relationship_type === "Guardian"
          ) || caseFileContacts[0];

        return {
          id: caseFile.id,
          case_number: caseFile.case_number,
          status: caseFile.status as CaseFileStatus,
          created_at: caseFile.created_at,
          activated_at: caseFile.activated_at,
          suspended_at: caseFile.suspended_at,
          closed_at: caseFile.closed_at,
          assigned_to: caseFile.assigned_to,
          primaryContactName: primaryContact?.contacts?.name || undefined,
          totalContacts: caseFileContacts.length,
          upcomingAppointments: 0, // TODO: Implement when appointments feature is ready
          pendingDocuments: 0, // TODO: Implement when documents feature is ready
        };
      });

      return successResponse({
        items: summaries,
        total: count || 0,
      });
    } catch (error) {
      logger.error(`Unexpected error listing case file summaries: ${error}`);
      return errorResponse(error, `Unexpected error listing case file summaries`);
    }
  }

  /**
   * Get active case files for dashboard
   * @returns Service response with active case files
   */
  static async getActiveCaseFiles(): Promise<ServiceResponse<CaseFile[]>> {
    try {
      // Use the list method to get active case files
      const response = await this.list({ status: "active", limit: 50 });

      if (!response.success || !response.data) {
        return errorResponse(null, response.message || "Failed to get active case files");
      }

      return successResponse(response.data.items, "Successfully retrieved active case files");
    } catch (error) {
      logger.error(`Unexpected error getting active case files: ${error}`);
      return errorResponse(error, `Unexpected error getting active case files`);
    }
  }

  /**
   * Get a case file by ID with optional related data
   * @param id The ID of the case file
   * @param includeContacts Whether to include contacts
   * @param includeHistory Whether to include history
   * @returns Service response with the case file and related data
   */
  static async getCaseFileById(
    id: string,
    includeContacts: boolean = true,
    includeHistory: boolean = false
  ): Promise<ServiceResponse<CaseFileWithRelations>> {
    try {
      // Use real database query
      const organization = await ProfileService.getCurrentOrganization();
      if (!organization) {
        return errorResponse(null, "Organization not found");
      }
      const organizationId = organization.id;

      const supabase = await createClient();

      // Get the case file with related data
      const { data, error } = await supabase
        .from("case_files")
        .select(
          `
          *,
          requests!case_files_request_id_fkey(
            id,
            reference_number,
            services!requests_service_id_fkey(id, name, description)
          ),
          employees!case_files_assigned_to_fkey(
            id,
            first_name,
            last_name,
            emails
          )
        `
        )
        .eq("id", id)
        .eq("organization_id", organizationId)
        .single();

      if (error) {
        if (error.code === "PGRST116") {
          // Record not found
          return errorResponse(error, `Case file with ID ${id} not found`);
        }

        logger.error(`Error reading case file: ${error.message}`);
        return errorResponse(error, `Failed to read case file: ${error.message}`);
      }

      const caseFile = data as CaseFile;

      // Create the CaseFileWithRelations object
      const caseFileWithRelations: CaseFileWithRelations = {
        ...caseFile,
      };

      // Process employee data if present
      if ((data as any).employees) {
        const employeeData = (data as any).employees;
        caseFileWithRelations.employees = {
          id: employeeData.id,
          first_name: employeeData.first_name,
          last_name: employeeData.last_name,
          email: this.extractPrimaryEmail(employeeData.emails),
        };
      }

      // Get contacts if requested
      if (includeContacts) {
        const { data: contactsData, error: contactsError } = await supabase
          .from("case_file_contacts")
          .select(
            `
            relationship_type,
            contacts!case_file_contacts_contact_id_fkey(
              id,
              name,
              email,
              phone
            )
          `
          )
          .eq("case_file_id", id);

        if (contactsError) {
          logger.error(`Error reading case file contacts: ${contactsError.message}`);
        } else {
          // Transform contacts data
          const contacts = (contactsData || []).map((item: any) => ({
            id: item.contacts.id,
            name: item.contacts.name,
            email: this.extractContactEmail(item.contacts.email),
            phone: this.extractContactPhone(item.contacts.phone),
            relationship_type: item.relationship_type,
          }));
          caseFileWithRelations.contacts = contacts;
        }
      }

      // Get history if requested
      if (includeHistory) {
        const { data: historyData, error: historyError } = await supabase
          .from("case_file_history")
          .select("*")
          .eq("case_file_id", id)
          .order("created_at", { ascending: false });

        if (historyError) {
          logger.error(`Error reading case file history: ${historyError.message}`);
        } else {
          caseFileWithRelations.history = historyData || [];
        }
      }

      return successResponse(caseFileWithRelations, "Successfully retrieved case file");
    } catch (error) {
      logger.error(`Unexpected error reading case file: ${error}`);
      return errorResponse(error, `Unexpected error reading case file`);
    }
  }

  /**
   * Get case file summary data for dashboard cards
   * @param id The ID of the case file
   * @returns Service response with case file summary
   */
  static async getCaseFileSummary(id: string): Promise<ServiceResponse<CaseFileSummary>> {
    try {
      const organization = await ProfileService.getCurrentOrganization();
      if (!organization) {
        return errorResponse(null, "Organization not found");
      }
      const organizationId = organization.id;

      const supabase = await createClient();

      // Get basic case file data
      const { data: caseFileData, error: caseFileError } = await supabase
        .from("case_files")
        .select("*")
        .eq("id", id)
        .eq("organization_id", organizationId)
        .single();

      if (caseFileError) {
        if (caseFileError.code === "PGRST116") {
          return errorResponse(caseFileError, `Case file with ID ${id} not found`);
        }
        logger.error(`Error reading case file: ${caseFileError.message}`);
        return errorResponse(caseFileError, `Failed to read case file: ${caseFileError.message}`);
      }

      // Get contact count and primary contact
      const { data: contactsData, error: contactsError } = await supabase
        .from("case_file_contacts")
        .select(
          `
          relationship_type,
          contacts!case_file_contacts_contact_id_fkey(name)
        `
        )
        .eq("case_file_id", id);

      let totalContacts = 0;
      let primaryContactName = undefined;

      if (!contactsError && contactsData) {
        totalContacts = contactsData.length;
        // Find primary contact (parent, guardian, or first contact)
        const primaryContact =
          contactsData.find(
            (c: any) => c.relationship_type === "parent" || c.relationship_type === "guardian"
          ) || contactsData[0];

        if (primaryContact) {
          primaryContactName = primaryContact.contacts.name;
        }
      }

      // TODO: Get upcoming appointments count (will be implemented with scheduling features)
      const upcomingAppointments = 0;

      // TODO: Get pending documents count (will be implemented with document features)
      const pendingDocuments = 0;

      const summary: CaseFileSummary = {
        id: caseFileData.id,
        case_number: caseFileData.case_number,
        status: caseFileData.status as CaseFileStatus,
        created_at: caseFileData.created_at,
        activated_at: caseFileData.activated_at,
        suspended_at: caseFileData.suspended_at,
        closed_at: caseFileData.closed_at,
        assigned_to: caseFileData.assigned_to,
        primaryContactName,
        totalContacts,
        upcomingAppointments,
        pendingDocuments,
      };

      return successResponse(summary, "Successfully retrieved case file summary");
    } catch (error) {
      logger.error(`Unexpected error getting case file summary: ${error}`);
      return errorResponse(error, `Unexpected error getting case file summary`);
    }
  }

  /**
   * Update case file status
   * @param id The ID of the case file
   * @param status The new status
   * @param notes Optional notes for the status change
   * @returns Service response with the updated case file
   */
  static async updateCaseFileStatus(
    id: string,
    status: CaseFileStatus,
    _notes?: string
  ): Promise<ServiceResponse<CaseFile>> {
    try {
      const organization = await ProfileService.getCurrentOrganization();
      if (!organization) {
        return errorResponse(null, "Organization not found");
      }
      const organizationId = organization.id;

      const supabase = await createClient();

      // Get current case file to track changes
      const { data: currentData, error: currentError } = await supabase
        .from("case_files")
        .select("*")
        .eq("id", id)
        .eq("organization_id", organizationId)
        .single();

      if (currentError) {
        logger.error(`Error reading current case file: ${currentError.message}`);
        return errorResponse(
          currentError,
          `Failed to read current case file: ${currentError.message}`
        );
      }

      // Prepare update data with timestamp for status change
      const updateData: CaseFileUpdate = {
        status,
        updated_at: new Date().toISOString(),
      };

      // Set appropriate timestamp based on status
      switch (status) {
        case "active":
          if (currentData.status !== "active") {
            updateData.activated_at = new Date().toISOString();
          }
          break;
        case "suspended":
          if (currentData.status !== "suspended") {
            updateData.suspended_at = new Date().toISOString();
          }
          break;
        case "closed":
          if (currentData.status !== "closed") {
            updateData.closed_at = new Date().toISOString();
          }
          break;
      }

      // Update the case file
      const { data, error } = await supabase
        .from("case_files")
        .update(updateData)
        .eq("id", id)
        .eq("organization_id", organizationId)
        .select()
        .single();

      if (error) {
        logger.error(`Error updating case file status: ${error.message}`);
        return errorResponse(error, `Failed to update case file status: ${error.message}`);
      }

      // TODO: Add history entry for status change
      // This will be implemented when CaseFileHistoryService is created

      return successResponse(data as CaseFile, "Successfully updated case file status");
    } catch (error) {
      logger.error(`Unexpected error updating case file status: ${error}`);
      return errorResponse(error, `Unexpected error updating case file status`);
    }
  }

  /**
   * Create a new case file
   * @param caseFile The case file to create
   * @returns Service response with the created case file
   */
  static async create(caseFile: CaseFileInsert): Promise<ServiceResponse<CaseFile>> {
    try {
      const supabase = await createClient();

      const { data, error } = await supabase.from("case_files").insert(caseFile).select().single();

      if (error) {
        logger.error(`Error creating case file: ${error.message}`);
        return errorResponse(error, `Failed to create case file: ${error.message}`);
      }

      return successResponse(data as CaseFile, "Successfully created case file");
    } catch (error) {
      logger.error(`Unexpected error creating case file: ${error}`);
      return errorResponse(error, `Unexpected error creating case file`);
    }
  }

  /**
   * Update an existing case file
   * @param id The ID of the case file to update
   * @param caseFile The updated case file data
   * @returns Service response with the updated case file
   */
  static async update(id: string, caseFile: CaseFileUpdate): Promise<ServiceResponse<CaseFile>> {
    try {
      const organization = await ProfileService.getCurrentOrganization();
      if (!organization) {
        return errorResponse(null, "Organization not found");
      }
      const organizationId = organization.id;

      const supabase = await createClient();

      // Add updated_at timestamp if not provided
      const updateData = {
        ...caseFile,
        updated_at: caseFile.updated_at || new Date().toISOString(),
      };

      const { data, error } = await supabase
        .from("case_files")
        .update(updateData)
        .eq("id", id)
        .eq("organization_id", organizationId)
        .select()
        .single();

      if (error) {
        logger.error(`Error updating case file: ${error.message}`);
        return errorResponse(error, `Failed to update case file: ${error.message}`);
      }

      return successResponse(data as CaseFile, "Successfully updated case file");
    } catch (error) {
      logger.error(`Unexpected error updating case file: ${error}`);
      return errorResponse(error, `Unexpected error updating case file`);
    }
  }
}
