import { createClient } from "@/lib/supabase/server";
import { logger } from "@/lib/logger/services/LoggerService";
import { CaseFileDashboardData, CaseFileWithRelations } from "../types";
import { ServiceResponse, successResponse, errorResponse } from "@/lib/types/responses";
import { CaseFileService } from "./CaseFileService";

/**
 * Service for aggregating case file data for dashboards and reports
 * Follows server-first paradigm with static methods
 */
export class CaseFileAggregationService {
  /**
   * Get comprehensive dashboard data for an active case file
   * @param caseFileId The ID of the case file
   * @returns Service response with aggregated dashboard data
   */
  static async getCaseFileDashboardData(
    caseFileId: string
  ): Promise<ServiceResponse<CaseFileDashboardData>> {
    try {
      // TODO: Replace with real database aggregation when ready
      // Get case file with relations using mock data
      const caseFileResponse = await CaseFileService.getCaseFileById(
        caseFileId,
        true, // include contacts
        false // don't include full history for dashboard
      );

      if (!caseFileResponse.success || !caseFileResponse.data) {
        return errorResponse(
          caseFileResponse.error,
          caseFileResponse.message || "Failed to get case file"
        );
      }

      const caseFile = caseFileResponse.data;

      // Create mock dashboard data
      const dashboardData: CaseFileDashboardData = {
        caseFile: caseFile,
        familyInfo: {
          primaryContact:
            caseFile.contacts && caseFile.contacts.length > 0
              ? {
                  name: caseFile.contacts[0].name,
                  relationship: caseFile.contacts[0].relationship_type,
                  phone: caseFile.contacts[0].phone || undefined,
                  email: caseFile.contacts[0].email || undefined,
                }
              : undefined,
          children: caseFile.contacts
            ? caseFile.contacts
                .filter((c) => c.relationship_type === "Child")
                .map((c) => ({
                  name: c.name,
                  age: 8, // Mock age
                  relationship: c.relationship_type,
                }))
            : [],
          totalContacts: caseFile.contacts ? caseFile.contacts.length : 0,
        },
        serviceRequirements: {
          totalServices: 4,
          completedServices: 2,
          upcomingAppointments: 3,
          pendingDocuments: 1,
        },
        recentActivity: [
          {
            id: "activity-1",
            type: "appointment",
            description: "Family assessment scheduled",
            timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(), // 2 hours ago
          },
          {
            id: "activity-2",
            type: "document",
            description: "Service agreement uploaded",
            timestamp: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString(), // 1 day ago
          },
          {
            id: "activity-3",
            type: "note",
            description: "Initial contact note added",
            timestamp: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000).toISOString(), // 3 days ago
          },
        ],
      };

      return successResponse(dashboardData, "Successfully retrieved dashboard data");

      // Original aggregation code (commented out for now)
      /*
      // Aggregate family information
      const familyInfo = await this.aggregateFamilyInformation(caseFile);

      // Aggregate service requirements
      const serviceRequirements = await this.aggregateServiceRequirements(caseFileId);

      // Get recent activity
      const recentActivity = await this.getRecentActivity(caseFileId);

      const dashboardData: CaseFileDashboardData = {
        caseFile,
        familyInfo,
        serviceRequirements,
        recentActivity,
      };

      return successResponse(dashboardData, "Successfully retrieved dashboard data");
      */
    } catch (error) {
      logger.error(`Unexpected error getting dashboard data: ${error}`);
      return errorResponse(error, `Unexpected error getting dashboard data`);
    }
  }

  /**
   * Aggregate family information from case file contacts
   * @param caseFile The case file with contacts
   * @returns Aggregated family information
   */
  private static aggregateFamilyInformation(caseFile: CaseFileWithRelations) {
    const contacts = caseFile.contacts || [];

    // Find primary contact (parent or guardian)
    const primaryContact = contacts.find(
      (contact) =>
        contact.relationship_type === "parent" || contact.relationship_type === "guardian"
    );

    // Find children
    const children = contacts
      .filter((contact) => contact.relationship_type === "child")
      .map((contact) => ({
        name: contact.name,
        age: this.calculateAge(contact), // TODO: Implement age calculation from contact data
        relationship: contact.relationship_type,
      }));

    return {
      primaryContact: primaryContact
        ? {
            name: primaryContact.name,
            relationship: primaryContact.relationship_type,
            phone: this.extractPhone(primaryContact.phone),
            email: this.extractEmail(primaryContact.email),
          }
        : undefined,
      children,
      totalContacts: contacts.length,
    };
  }

  /**
   * Aggregate service requirements and progress
   * @param caseFileId The ID of the case file
   * @returns Aggregated service requirements data
   */
  private static async aggregateServiceRequirements(_caseFileId: string) {
    try {
      const _supabase = await createClient();

      // TODO: These queries will be implemented when scheduling and document features are added
      // For now, return placeholder data

      // Get appointments count (placeholder)
      const upcomingAppointments = 0;

      // Get documents count (placeholder)
      const pendingDocuments = 0;

      // Calculate service progress (placeholder)
      const totalServices = 4; // Based on typical family service requirements
      const completedServices = 1; // Placeholder

      return {
        totalServices,
        completedServices,
        upcomingAppointments,
        pendingDocuments,
      };
    } catch (error) {
      logger.error(`Error aggregating service requirements: ${error}`);
      // Return default values on error
      return {
        totalServices: 0,
        completedServices: 0,
        upcomingAppointments: 0,
        pendingDocuments: 0,
      };
    }
  }

  /**
   * Get recent activity for the case file
   * @param caseFileId The ID of the case file
   * @returns Array of recent activity items
   */
  private static async getRecentActivity(caseFileId: string) {
    try {
      const supabase = await createClient();

      // Get recent case file history
      const { data: historyData, error: historyError } = await supabase
        .from("case_file_history")
        .select("*")
        .eq("case_file_id", caseFileId)
        .order("created_at", { ascending: false })
        .limit(5);

      if (historyError) {
        logger.error(`Error getting recent activity: ${historyError.message}`);
        return [];
      }

      // Transform history data to activity items
      const activityItems = (historyData || [])
        .filter((item) => item.created_at) // Filter out items without timestamp
        .map((item) => ({
          id: item.id,
          type: this.mapActionToActivityType(item.action),
          description: this.formatActivityDescription(item.action, item.changes),
          timestamp: item.created_at as string,
        }));

      // TODO: Add appointments, documents, and notes to recent activity
      // This will be implemented when those features are added

      return activityItems;
    } catch (error) {
      logger.error(`Error getting recent activity: ${error}`);
      return [];
    }
  }

  /**
   * Get case file statistics for organization dashboard
   * @returns Service response with case file statistics
   */
  static async getCaseFileStatistics(): Promise<
    ServiceResponse<{
      opening: number;
      active: number;
      suspended: number;
      closed: number;
      total: number;
    }>
  > {
    try {
      // TODO: Replace with real database query when ready
      // Return mock statistics based on our mock case files
      const stats = {
        opening: 1,
        active: 1,
        suspended: 1,
        closed: 1,
        total: 4,
      };

      return successResponse(stats, "Successfully retrieved case file statistics");
    } catch (error) {
      logger.error(`Unexpected error getting case file statistics: ${error}`);
      return errorResponse(error, `Unexpected error getting case file statistics`);
    }
  }

  /**
   * Helper method to calculate age from contact data
   * @param contact The contact object
   * @returns Calculated age or undefined
   */
  private static calculateAge(_contact: any): number | undefined {
    // TODO: Implement age calculation when birth date is available in contact data
    // For now, return undefined
    return undefined;
  }

  /**
   * Helper method to extract phone number from contact phone data
   * @param phoneData The phone data object
   * @returns Primary phone number or undefined
   */
  private static extractPhone(phoneData: any): string | undefined {
    if (!phoneData) return undefined;

    // Handle different phone data structures
    if (typeof phoneData === "string") return phoneData;
    if (typeof phoneData === "object") {
      return phoneData.mobile || phoneData.home || phoneData.work || phoneData.other;
    }

    return undefined;
  }

  /**
   * Helper method to extract email from contact email data
   * @param emailData The email data object
   * @returns Primary email or undefined
   */
  private static extractEmail(emailData: any): string | undefined {
    if (!emailData) return undefined;

    // Handle different email data structures
    if (typeof emailData === "string") return emailData;
    if (typeof emailData === "object") {
      return emailData.personal || emailData.work || emailData.other;
    }

    return undefined;
  }

  /**
   * Helper method to map history action to activity type
   * @param action The history action
   * @returns Activity type
   */
  private static mapActionToActivityType(
    action: string
  ): "appointment" | "document" | "note" | "status_change" {
    if (action.includes("status")) return "status_change";
    if (action.includes("appointment")) return "appointment";
    if (action.includes("document")) return "document";
    if (action.includes("note")) return "note";
    return "status_change";
  }

  /**
   * Helper method to format activity description
   * @param action The history action
   * @param changes The changes data
   * @returns Formatted description
   */
  private static formatActivityDescription(action: string, _changes: any): string {
    // TODO: Implement more sophisticated description formatting
    switch (action) {
      case "status_change":
        return "Case file status updated";
      case "created":
        return "Case file created";
      case "assignment":
        return "Case file assigned";
      default:
        return action.replace("_", " ").toLowerCase();
    }
  }
}
