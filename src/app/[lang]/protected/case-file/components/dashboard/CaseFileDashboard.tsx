import { Badge } from "@/components/ui/badge";
import { H1, P, Lead } from "@/components/typography";
import { FileText, Clock } from "lucide-react";
import { CaseFileDashboardData } from "../../lib/types";
import { FamilyInformation } from "./FamilyInformation";
import { ServiceRequirements } from "./ServiceRequirements";
import { QuickActions } from "./QuickActions";
import { RecentActivity } from "./RecentActivity";

interface CaseFileDashboardProps {
  data: CaseFileDashboardData;
  lang: string;
}

/**
 * Main dashboard component for active case files
 * Displays comprehensive case file information and management tools
 */
export function CaseFileDashboard({ data, lang }: CaseFileDashboardProps) {
  const { caseFile, familyInfo, serviceRequirements, recentActivity } = data;

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "active":
        return (
          <Badge variant="default" className="bg-green-100 text-green-800">
            <Clock className="h-3 w-3 mr-1" />
            Active
          </Badge>
        );
      case "opening":
        return (
          <Badge variant="default" className="bg-orange-100 text-orange-800">
            <Clock className="h-3 w-3 mr-1" />
            Opening
          </Badge>
        );
      case "suspended":
        return (
          <Badge variant="default" className="bg-yellow-100 text-yellow-800">
            <Clock className="h-3 w-3 mr-1" />
            Suspended
          </Badge>
        );
      case "closed":
        return (
          <Badge variant="default" className="bg-gray-100 text-gray-800">
            <Clock className="h-3 w-3 mr-1" />
            Closed
          </Badge>
        );
      default:
        return (
          <Badge variant="secondary">
            <Clock className="h-3 w-3 mr-1" />
            {status}
          </Badge>
        );
    }
  };

  const formatActivationDate = (activatedAt: string | null) => {
    if (!activatedAt) return "Active";
    return `Active since ${new Date(activatedAt).toLocaleDateString()}`;
  };

  const formatAssignment = (assignedTo: string | null) => {
    if (!assignedTo) return "Unassigned";
    return `Assigned to: ${assignedTo}`;
  };

  return (
    <div className="space-y-6">
      {/* Case File Header */}
      <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
        <div className="flex items-center">
          <div className="bg-primary/10 p-2 rounded-full mr-3">
            <FileText className="h-10 w-10 text-primary" />
          </div>
          <div>
            <H1>Case File {caseFile.case_number}</H1>
            <Lead className="mt-1">{formatActivationDate(caseFile.activated_at)}</Lead>
          </div>
        </div>

        <div className="flex items-center gap-4">
          {getStatusBadge(caseFile.status)}
          <P className="text-sm text-muted-foreground">{formatAssignment(caseFile.assigned_to)}</P>
        </div>
      </div>

      {/* Quick Actions */}
      <QuickActions caseFileId={caseFile.id} lang={lang} />

      {/* Dashboard Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Family Information */}
        <FamilyInformation familyInfo={familyInfo} caseFileId={caseFile.id} lang={lang} />

        {/* Service Requirements */}
        <ServiceRequirements
          serviceRequirements={serviceRequirements}
          caseFileId={caseFile.id}
          lang={lang}
        />
      </div>

      {/* Recent Activity */}
      <RecentActivity recentActivity={recentActivity} caseFileId={caseFile.id} lang={lang} />
    </div>
  );
}
