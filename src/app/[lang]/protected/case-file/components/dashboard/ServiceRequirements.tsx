import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON>eader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { P } from "@/components/typography";
import { FileText, AlertCircle } from "lucide-react";
import { CaseFileDashboardData } from "../../lib/types";

interface ServiceRequirementsProps {
  serviceRequirements: CaseFileDashboardData["serviceRequirements"];
  caseFileId: string;
  lang: string;
}

/**
 * Service Requirements component for case file dashboard
 * Displays service progress and pending items
 */
export function ServiceRequirements({ serviceRequirements, caseFileId: _caseFileId, lang: _lang }: ServiceRequirementsProps) {
  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center">
          <FileText className="h-5 w-5 mr-2" />
          Service Progress
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Progress Stats */}
        <div className="grid grid-cols-2 gap-4">
          <div className="text-center p-4 bg-blue-50 rounded-lg">
            <div className="text-2xl font-bold text-blue-600">
              {serviceRequirements.completedServices}/{serviceRequirements.totalServices}
            </div>
            <P className="text-sm text-blue-600">Services Completed</P>
          </div>
          <div className="text-center p-4 bg-green-50 rounded-lg">
            <div className="text-2xl font-bold text-green-600">
              {serviceRequirements.upcomingAppointments}
            </div>
            <P className="text-sm text-green-600">Upcoming Appointments</P>
          </div>
        </div>

        {/* Pending Items */}
        {serviceRequirements.pendingDocuments > 0 && (
          <div className="flex items-center p-3 bg-yellow-50 rounded-lg">
            <AlertCircle className="h-5 w-5 text-yellow-600 mr-2" />
            <div>
              <P className="text-sm font-medium text-yellow-800">
                {serviceRequirements.pendingDocuments} pending document(s)
              </P>
              <P className="text-xs text-yellow-600">
                Requires attention
              </P>
            </div>
          </div>
        )}

        <div className="space-y-2">
          <Button variant="outline" size="sm" className="w-full">
            View Service Timeline
          </Button>
          <Button variant="outline" size="sm" className="w-full">
            View All Appointments
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}
