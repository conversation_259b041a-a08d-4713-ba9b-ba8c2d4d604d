import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Title } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { P } from "@/components/typography";
import { Calendar, FileText, Edit, Clock } from "lucide-react";
import { CaseFileDashboardData } from "../../lib/types";

interface RecentActivityProps {
  recentActivity: CaseFileDashboardData["recentActivity"];
  caseFileId: string;
  lang: string;
}

/**
 * Recent Activity component for case file dashboard
 * Displays timeline of recent case file activities
 */
export function RecentActivity({ recentActivity, caseFileId: _caseFileId, lang: _lang }: RecentActivityProps) {
  const getActivityIcon = (type: string) => {
    switch (type) {
      case "appointment":
        return <Calendar className="h-4 w-4 text-blue-500 mr-2" />;
      case "document":
        return <FileText className="h-4 w-4 text-green-500 mr-2" />;
      case "note":
        return <Edit className="h-4 w-4 text-purple-500 mr-2" />;
      case "status_change":
        return <Clock className="h-4 w-4 text-orange-500 mr-2" />;
      default:
        return <FileText className="h-4 w-4 text-gray-500 mr-2" />;
    }
  };

  const formatTimestamp = (timestamp: string) => {
    const date = new Date(timestamp);
    const now = new Date();
    const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60));

    if (diffInHours < 1) return "Just now";
    if (diffInHours < 24) return `${diffInHours} hour${diffInHours > 1 ? 's' : ''} ago`;

    const diffInDays = Math.floor(diffInHours / 24);
    return `${diffInDays} day${diffInDays > 1 ? 's' : ''} ago`;
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Recent Activity</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-3">
          {recentActivity.length > 0 ? (
            recentActivity.map((activity) => (
              <div key={activity.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <div className="flex items-center">
                  {getActivityIcon(activity.type)}
                  <span className="text-sm">{activity.description}</span>
                </div>
                <span className="text-xs text-muted-foreground">
                  {formatTimestamp(activity.timestamp)}
                </span>
              </div>
            ))
          ) : (
            <div className="text-center py-8 text-muted-foreground">
              <FileText className="h-8 w-8 mx-auto mb-2 opacity-50" />
              <P className="text-sm">No recent activity</P>
            </div>
          )}
        </div>
        <div className="mt-4">
          <Button variant="outline" size="sm" className="w-full">
            View Full History
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}
