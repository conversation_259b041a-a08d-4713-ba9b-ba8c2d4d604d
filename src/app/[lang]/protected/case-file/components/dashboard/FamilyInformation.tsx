import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { H2, P } from "@/components/typography";
import { Users } from "lucide-react";
import { CaseFileDashboardData } from "../../lib/types";

interface FamilyInformationProps {
  familyInfo: CaseFileDashboardData["familyInfo"];
  caseFileId: string;
  lang: string;
}

/**
 * Family Information component for case file dashboard
 * Displays primary contact and children information
 */
export function FamilyInformation({
  familyInfo,
  caseFileId: _caseFileId,
  lang: _lang,
}: FamilyInformationProps) {
  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center">
          <Users className="h-5 w-5 mr-2" />
          Family Information
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Primary Contact */}
        {familyInfo.primaryContact ? (
          <div className="border-b pb-4">
            <H2>{familyInfo.primaryContact.name}</H2>
            <P>{familyInfo.primaryContact.relationship}</P>
            <div className="mt-2 space-y-1">
              <P>{familyInfo.primaryContact.phone || "No phone"}</P>
              <P>{familyInfo.primaryContact.email || "No email"}</P>
            </div>
          </div>
        ) : (
          <div className="border-b pb-4">
            <H2>No Primary Contact</H2>
            <P>No primary contact assigned</P>
          </div>
        )}

        {/* Children */}
        <div>
          <H2 className="mb-2">Children</H2>
          {familyInfo.children.length > 0 ? (
            <div className="space-y-2">
              {familyInfo.children.map((child, index) => (
                <div key={index} className="flex justify-between items-center">
                  <span>{child.name}</span>
                  {child.age && <Badge variant="secondary">{child.age} years old</Badge>}
                </div>
              ))}
            </div>
          ) : (
            <P>No children listed</P>
          )}
        </div>

        <div className="pt-4 border-t">
          <Button variant="outline">View All Contacts ({familyInfo.totalContacts})</Button>
        </div>
      </CardContent>
    </Card>
  );
}
