"use server";

import { revalidatePath } from "next/cache";
import { z } from "zod";
import { CaseFileService } from "../lib/services/CaseFileService";
import { CaseFileStatus } from "../lib/types";
import { ActionState } from "@/lib/types/responses";
import { logger } from "@/lib/logger/services/LoggerService";
import { requirePermission } from "@/lib/authorization/utils/withPermissionDecorator";
import { DOMAIN_PERMISSIONS } from "../lib/config/domain";

// Validation schemas
const activateCaseFileSchema = z.object({
  notes: z.string().optional(),
});

const suspendCaseFileSchema = z.object({
  reason: z.string().min(1, "Suspension reason is required"),
  notes: z.string().optional(),
});

const closeCaseFileSchema = z.object({
  reason: z.string().min(1, "Closure reason is required"),
  notes: z.string().optional(),
});

/**
 * Activate a case file (transition from opening to active)
 * @param id The ID of the case file to activate
 * @param formData Optional form data with notes
 * @returns ActionState with success or error
 */
export const activateCaseFile = requirePermission(DOMAIN_PERMISSIONS.ACTIVATE)(async (
  id: string,
  formData?: FormData
): Promise<ActionState<{ id: string } | null>> => {
  try {
    // Validate required fields
    if (!id) {
      return {
        success: false,
        error: "Case file ID is required",
        data: null,
      };
    }

    // Extract and validate form data if provided
    let notes: string | undefined;
    if (formData) {
      const rawData = Object.fromEntries(formData.entries());
      const validationResult = activateCaseFileSchema.safeParse(rawData);

      if (!validationResult.success) {
        const errors = validationResult.error.format();
        return {
          success: false,
          error: "Validation failed: " + JSON.stringify(errors),
          data: null,
        };
      }

      notes = validationResult.data.notes;
    }

    // Update the case file status to active
    const response = await CaseFileService.updateCaseFileStatus(id, "active", notes);

    if (!response.success || !response.data) {
      return {
        success: false,
        error: response.message || "Failed to activate case file",
        data: null,
      };
    }

    // Revalidate relevant pages
    revalidatePath("/[lang]/protected/case-file");
    revalidatePath(`/[lang]/protected/case-file/[id]`, "page");

    return {
      success: true,
      error: "",
      data: { id: response.data.id },
    };
  } catch (error) {
    logger.error(`Unexpected error activating case file: ${error}`);
    return {
      success: false,
      error: `Unexpected error activating case file: ${error}`,
      data: null,
    };
  }
});

/**
 * Suspend an active case file
 * @param id The ID of the case file to suspend
 * @param formData Form data with suspension reason and notes
 * @returns ActionState with success or error
 */
export const suspendCaseFile = requirePermission(DOMAIN_PERMISSIONS.SUSPEND)(async (
  id: string,
  formData: FormData
): Promise<ActionState<{ id: string } | null>> => {
  try {
    // Validate required fields
    if (!id) {
      return {
        success: false,
        error: "Case file ID is required",
        data: null,
      };
    }

    // Extract and validate form data
    const rawData = Object.fromEntries(formData.entries());
    const validationResult = suspendCaseFileSchema.safeParse(rawData);

    if (!validationResult.success) {
      const errors = validationResult.error.format();
      return {
        success: false,
        error: "Validation failed: " + JSON.stringify(errors),
        data: null,
      };
    }

    const { reason, notes } = validationResult.data;

    // Combine reason and notes for the status update
    const statusNotes = notes ? `${reason}. ${notes}` : reason;

    // Update the case file status to suspended
    const response = await CaseFileService.updateCaseFileStatus(id, "suspended", statusNotes);

    if (!response.success || !response.data) {
      return {
        success: false,
        error: response.message || "Failed to suspend case file",
        data: null,
      };
    }

    // Revalidate relevant pages
    revalidatePath("/[lang]/protected/case-file");
    revalidatePath(`/[lang]/protected/case-file/[id]`, "page");

    return {
      success: true,
      error: "",
      data: { id: response.data.id },
    };
  } catch (error) {
    logger.error(`Unexpected error suspending case file: ${error}`);
    return {
      success: false,
      error: `Unexpected error suspending case file: ${error}`,
      data: null,
    };
  }
});

/**
 * Close an active case file
 * @param id The ID of the case file to close
 * @param formData Form data with closure reason and notes
 * @returns ActionState with success or error
 */
export const closeCaseFile = requirePermission(DOMAIN_PERMISSIONS.CLOSE)(async (
  id: string,
  formData: FormData
): Promise<ActionState<{ id: string } | null>> => {
  try {
    // Validate required fields
    if (!id) {
      return {
        success: false,
        error: "Case file ID is required",
        data: null,
      };
    }

    // Extract and validate form data
    const rawData = Object.fromEntries(formData.entries());
    const validationResult = closeCaseFileSchema.safeParse(rawData);

    if (!validationResult.success) {
      const errors = validationResult.error.format();
      return {
        success: false,
        error: "Validation failed: " + JSON.stringify(errors),
        data: null,
      };
    }

    const { reason, notes } = validationResult.data;

    // Combine reason and notes for the status update
    const statusNotes = notes ? `${reason}. ${notes}` : reason;

    // Update the case file status to closed
    const response = await CaseFileService.updateCaseFileStatus(id, "closed", statusNotes);

    if (!response.success || !response.data) {
      return {
        success: false,
        error: response.message || "Failed to close case file",
        data: null,
      };
    }

    // Revalidate relevant pages
    revalidatePath("/[lang]/protected/case-file");
    revalidatePath(`/[lang]/protected/case-file/[id]`, "page");

    return {
      success: true,
      error: "",
      data: { id: response.data.id },
    };
  } catch (error) {
    logger.error(`Unexpected error closing case file: ${error}`);
    return {
      success: false,
      error: `Unexpected error closing case file: ${error}`,
      data: null,
    };
  }
});

/**
 * Reactivate a suspended case file
 * @param id The ID of the case file to reactivate
 * @param formData Optional form data with notes
 * @returns ActionState with success or error
 */
export const reactivateCaseFile = requirePermission(DOMAIN_PERMISSIONS.ACTIVATE)(async (
  id: string,
  formData?: FormData
): Promise<ActionState<{ id: string } | null>> => {
  try {
    // Validate required fields
    if (!id) {
      return {
        success: false,
        error: "Case file ID is required",
        data: null,
      };
    }

    // Extract and validate form data if provided
    let notes: string | undefined;
    if (formData) {
      const rawData = Object.fromEntries(formData.entries());
      const validationResult = activateCaseFileSchema.safeParse(rawData);

      if (!validationResult.success) {
        const errors = validationResult.error.format();
        return {
          success: false,
          error: "Validation failed: " + JSON.stringify(errors),
          data: null,
        };
      }

      notes = validationResult.data.notes;
    }

    // Update the case file status to active
    const response = await CaseFileService.updateCaseFileStatus(id, "active", notes);

    if (!response.success || !response.data) {
      return {
        success: false,
        error: response.message || "Failed to reactivate case file",
        data: null,
      };
    }

    // Revalidate relevant pages
    revalidatePath("/[lang]/protected/case-file");
    revalidatePath(`/[lang]/protected/case-file/[id]`, "page");

    return {
      success: true,
      error: "",
      data: { id: response.data.id },
    };
  } catch (error) {
    logger.error(`Unexpected error reactivating case file: ${error}`);
    return {
      success: false,
      error: `Unexpected error reactivating case file: ${error}`,
      data: null,
    };
  }
});

/**
 * Get the current status of a case file
 * @param id The ID of the case file
 * @returns ActionState with the case file status
 */
export const getCaseFileStatus = requirePermission(DOMAIN_PERMISSIONS.VIEW)(async (
  id: string
): Promise<ActionState<{ status: CaseFileStatus; id: string } | null>> => {
  try {
    // Validate required fields
    if (!id) {
      return {
        success: false,
        error: "Case file ID is required",
        data: null,
      };
    }

    // Get the case file
    const response = await CaseFileService.getCaseFileById(id, false, false);

    if (!response.success || !response.data) {
      return {
        success: false,
        error: response.message || "Failed to get case file status",
        data: null,
      };
    }

    return {
      success: true,
      error: "",
      data: {
        id: response.data.id,
        status: response.data.status,
      },
    };
  } catch (error) {
    logger.error(`Unexpected error getting case file status: ${error}`);
    return {
      success: false,
      error: `Unexpected error getting case file status: ${error}`,
      data: null,
    };
  }
});
