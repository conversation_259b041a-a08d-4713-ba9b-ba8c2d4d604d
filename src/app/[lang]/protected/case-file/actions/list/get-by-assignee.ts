"use server";

import { CaseFileService } from "../../lib/services";
import { CaseFile } from "../../lib/types";
import { ActionState } from "@/lib/types/responses";
import { logger } from "@/lib/logger/services/LoggerService";
import { requirePermission } from "@/lib/authorization/utils/withPermissionDecorator";
import { DOMAIN_PERMISSIONS } from "../../lib/config/domain";

/**
 * Get case files assigned to a specific employee
 * @param employeeId The ID of the employee
 * @param limit Maximum number of results to return
 * @returns ActionState with case files assigned to the employee
 */
export const getCaseFilesByAssignee = requirePermission(DOMAIN_PERMISSIONS.VIEW)(async (
  employeeId: string,
  limit: number = 50
): Promise<ActionState<CaseFile[] | null>> => {
  try {
    // Validate required fields
    if (!employeeId) {
      return {
        success: false,
        error: "Employee ID is required",
        data: null,
      };
    }

    // Get case files by assignee using the list function
    const response = await CaseFileService.list({
      assigned_to: employeeId,
      limit,
      page: 1,
      sortBy: "updated_at",
      sortOrder: "desc",
    });

    if (!response.success || !response.data) {
      return {
        success: false,
        error: response.message || "Failed to get case files by assignee",
        data: null,
      };
    }

    return {
      success: true,
      error: "",
      data: response.data.items,
    };
  } catch (error) {
    logger.error(`Unexpected error getting case files by assignee: ${error}`);
    return {
      success: false,
      error: `Unexpected error getting case files by assignee: ${error}`,
      data: null,
    };
  }
});
