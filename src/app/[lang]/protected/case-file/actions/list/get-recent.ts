"use server";

// Note: This file needs to be updated to use the new domain architecture
import { CaseFile } from "../../lib/types";
import { ActionState } from "@/lib/types/responses";
import { logger } from "@/lib/logger/services/LoggerService";
import { requirePermission } from "@/lib/authorization/utils/withPermissionDecorator";
import { DOMAIN_PERMISSIONS } from "../../lib/config/domain";

/**
 * Get recent case files (last 30 days)
 * @param limit Maximum number of results to return
 * @returns ActionState with recent case files
 */
export const getRecentCaseFiles = requirePermission(DOMAIN_PERMISSIONS.VIEW)(async (
  limit: number = 20
): Promise<ActionState<CaseFile[] | null>> => {
  try {
    // TODO: Implement using new domain architecture
    return {
      success: false,
      error: "This function needs to be updated to use the new domain architecture",
      data: null,
    };
  } catch (error) {
    logger.error(`Unexpected error getting recent case files: ${error}`);
    return {
      success: false,
      error: `Unexpected error getting recent case files: ${error}`,
      data: null,
    };
  }
});
