"use server";

import { CaseFileService } from "../../lib/services";
import { CaseFile } from "../../lib/types";
import { ActionState } from "@/lib/types/responses";
import { logger } from "@/lib/logger/services/LoggerService";
import { requirePermission } from "@/lib/authorization/utils/withPermissionDecorator";
import { DOMAIN_PERMISSIONS } from "../../lib/config/domain";

/**
 * Get recent case files (last 30 days)
 * @param limit Maximum number of results to return
 * @returns ActionState with recent case files
 */
export const getRecentCaseFiles = requirePermission(DOMAIN_PERMISSIONS.VIEW)(async (
  limit: number = 20
): Promise<ActionState<CaseFile[] | null>> => {
  try {
    // Calculate date 30 days ago
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

    // Get recent case files using the list function
    const response = await CaseFileService.list({
      from_date: thirtyDaysAgo.toISOString(),
      limit,
      page: 1,
      sortBy: "created_at",
      sortOrder: "desc",
    });

    if (!response.success || !response.data) {
      return {
        success: false,
        error: response.message || "Failed to get recent case files",
        data: null,
      };
    }

    return {
      success: true,
      error: "",
      data: response.data.items,
    };
  } catch (error) {
    logger.error(`Unexpected error getting recent case files: ${error}`);
    return {
      success: false,
      error: `Unexpected error getting recent case files: ${error}`,
      data: null,
    };
  }
});
