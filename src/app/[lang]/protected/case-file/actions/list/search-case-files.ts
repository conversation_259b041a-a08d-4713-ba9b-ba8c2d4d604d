"use server";

import { CaseFileService } from "../../lib/services";
import { CaseFile } from "../../lib/types";
import { ActionState } from "@/lib/types/responses";
import { logger } from "@/lib/logger/services/LoggerService";
import { requirePermission } from "@/lib/authorization/utils/withPermissionDecorator";
import { DOMAIN_PERMISSIONS } from "../../lib/config/domain";

/**
 * Search case files by case number or contact name
 * @param query Search query string
 * @param limit Maximum number of results to return
 * @returns ActionState with matching case files
 */
export const searchCaseFiles = requirePermission(DOMAIN_PERMISSIONS.VIEW)(async (
  query: string,
  limit: number = 10
): Promise<ActionState<CaseFile[] | null>> => {
  try {
    // Validate required fields
    if (!query || query.trim().length === 0) {
      return {
        success: true,
        error: "",
        data: [],
      };
    }

    // Search case files using the list function with search parameter
    const response = await CaseFileService.list({
      search: query.trim(),
      limit,
      page: 1,
      sortBy: "updated_at",
      sortOrder: "desc",
    });

    if (!response.success || !response.data) {
      return {
        success: false,
        error: response.message || "Failed to search case files",
        data: null,
      };
    }

    return {
      success: true,
      error: "",
      data: response.data.items,
    };
  } catch (error) {
    logger.error(`Unexpected error searching case files: ${error}`);
    return {
      success: false,
      error: `Unexpected error searching case files: ${error}`,
      data: null,
    };
  }
});
