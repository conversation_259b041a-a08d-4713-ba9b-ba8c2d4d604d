"use server";

import { CaseFileService } from "../../lib/services";
import { CaseFile } from "../../lib/types";
import { ActionState } from "@/lib/types/responses";
import { logger } from "@/lib/logger/services/LoggerService";
import { requirePermission } from "@/lib/authorization/utils/withPermissionDecorator";
import { DOMAIN_PERMISSIONS } from "../../lib/config/domain";

/**
 * Get case files by status
 * @param status The status to filter by
 * @param limit Maximum number of results to return
 * @returns ActionState with case files of the specified status
 */
export const getCaseFilesByStatus = requirePermission(DOMAIN_PERMISSIONS.VIEW)(async (
  status: "opening" | "active" | "suspended" | "closed",
  limit: number = 50
): Promise<ActionState<CaseFile[] | null>> => {
  try {
    // Get case files by status using the list function
    const response = await CaseFileService.list({
      status,
      limit,
      page: 1,
      sortBy: "updated_at",
      sortOrder: "desc",
    });

    if (!response.success || !response.data) {
      return {
        success: false,
        error: response.message || "Failed to get case files by status",
        data: null,
      };
    }

    return {
      success: true,
      error: "",
      data: response.data.items,
    };
  } catch (error) {
    logger.error(`Unexpected error getting case files by status: ${error}`);
    return {
      success: false,
      error: `Unexpected error getting case files by status: ${error}`,
      data: null,
    };
  }
});
