"use server";

import { CaseFileService } from "../../lib/services";
import { CaseFileListParams, CaseFileSummary } from "../../lib/types";
import { ActionState } from "@/lib/types/responses";
import { logger } from "@/lib/logger/services/LoggerService";
import { requirePermission } from "@/lib/authorization/utils/withPermissionDecorator";
import { DOMAIN_PERMISSIONS } from "../../lib/config/domain";

/**
 * List case file summaries with aggregated data for list views
 * @param params Parameters for filtering, pagination, and sorting
 * @returns ActionState with case file summaries and total count
 */
export const listCaseFileSummaries = requirePermission(DOMAIN_PERMISSIONS.VIEW)(async (
  params: CaseFileListParams = {}
): Promise<ActionState<{ items: CaseFileSummary[]; total: number }>> => {
  try {
    // Validate parameters
    if (params.page && params.page < 1) {
      return {
        success: true,
        error: "Page number must be greater than 0",
        data: {
          items: [],
          total: 0,
        },
      };
    }

    if (params.limit && (params.limit < 1 || params.limit > 100)) {
      return {
        success: true,
        error: "Limit must be between 1 and 100",
        data: {
          items: [],
          total: 0,
        },
      };
    }

    // Get case file summaries from service
    const response = await CaseFileService.listSummaries(params);

    if (!response.success || !response.data) {
      // Return empty result structure instead of null for consistent handling
      return {
        success: true,
        error: response.message || "Failed to list case file summaries",
        data: {
          items: [],
          total: 0,
        },
      };
    }

    return {
      success: true,
      error: "",
      data: response.data,
    };
  } catch (error) {
    logger.error(`Unexpected error listing case file summaries: ${error}`);
    return {
      success: true,
      error: `Unexpected error listing case file summaries: ${error}`,
      data: {
        items: [],
        total: 0,
      },
    };
  }
});
