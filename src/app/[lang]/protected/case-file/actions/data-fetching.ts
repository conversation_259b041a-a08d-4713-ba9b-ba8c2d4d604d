"use server";

import { CaseFileService, CaseFileAggregationService } from "../lib/services";
import {
  CaseFileWithRelations,
  CaseFileDashboardData,
  CaseFileSummary,
  CaseFileStatus,
} from "../lib/types";
import { ActionState } from "@/lib/types/responses";
import { logger } from "@/lib/logger/services/LoggerService";
import { requirePermission } from "@/lib/authorization/utils/withPermissionDecorator";
import { DOMAIN_PERMISSIONS } from "../lib/config/domain";

/**
 * Get comprehensive dashboard data for an active case file
 * @param caseFileId The ID of the case file
 * @returns ActionState with aggregated dashboard data
 */
export const getCaseFileDashboardData = requirePermission(DOMAIN_PERMISSIONS.VIEW)(async (
  caseFileId: string
): Promise<ActionState<CaseFileDashboardData | null>> => {
  try {
    // Validate required fields
    if (!caseFileId) {
      return {
        success: false,
        error: "Case file ID is required",
        data: null,
      };
    }

    // Get dashboard data from aggregation service
    const response = await CaseFileAggregationService.getCaseFileDashboardData(caseFileId);

    if (!response.success || !response.data) {
      return {
        success: false,
        error: response.message || "Failed to get dashboard data",
        data: null,
      };
    }

    return {
      success: true,
      error: "",
      data: response.data,
    };
  } catch (error) {
    logger.error(`Unexpected error getting dashboard data: ${error}`);
    return {
      success: false,
      error: `Unexpected error getting dashboard data: ${error}`,
      data: null,
    };
  }
});

/**
 * Get case file summary data for cards and lists
 * @param caseFileId The ID of the case file
 * @returns ActionState with case file summary
 */
export const getCaseFileSummaryData = requirePermission(DOMAIN_PERMISSIONS.VIEW)(async (
  caseFileId: string
): Promise<ActionState<CaseFileSummary | null>> => {
  try {
    // Validate required fields
    if (!caseFileId) {
      return {
        success: false,
        error: "Case file ID is required",
        data: null,
      };
    }

    // Get summary data from service
    const response = await CaseFileService.getCaseFileSummary(caseFileId);

    if (!response.success || !response.data) {
      return {
        success: false,
        error: response.message || "Failed to get case file summary",
        data: null,
      };
    }

    return {
      success: true,
      error: "",
      data: response.data,
    };
  } catch (error) {
    logger.error(`Unexpected error getting case file summary: ${error}`);
    return {
      success: false,
      error: `Unexpected error getting case file summary: ${error}`,
      data: null,
    };
  }
});

/**
 * Get case file contacts with relationship information
 * @param caseFileId The ID of the case file
 * @returns ActionState with case file contacts
 */
export const getCaseFileContacts = requirePermission(DOMAIN_PERMISSIONS.VIEW)(async (
  caseFileId: string
): Promise<ActionState<CaseFileWithRelations["contacts"] | null>> => {
  try {
    // Validate required fields
    if (!caseFileId) {
      return {
        success: false,
        error: "Case file ID is required",
        data: null,
      };
    }

    // Get case file with contacts
    const response = await CaseFileService.getCaseFileById(
      caseFileId,
      true, // include contacts
      false // don't include history
    );

    if (!response.success || !response.data) {
      return {
        success: false,
        error: response.message || "Failed to get case file contacts",
        data: null,
      };
    }

    return {
      success: true,
      error: "",
      data: response.data.contacts || [],
    };
  } catch (error) {
    logger.error(`Unexpected error getting case file contacts: ${error}`);
    return {
      success: false,
      error: `Unexpected error getting case file contacts: ${error}`,
      data: null,
    };
  }
});

/**
 * Get case file with full details and relations
 * @param caseFileId The ID of the case file
 * @param includeContacts Whether to include contacts
 * @param includeHistory Whether to include history
 * @returns ActionState with case file and relations
 */
export const getCaseFileWithRelations = requirePermission(DOMAIN_PERMISSIONS.VIEW)(async (
  caseFileId: string,
  includeContacts: boolean = true,
  includeHistory: boolean = false
): Promise<ActionState<CaseFileWithRelations | null>> => {
  try {
    // Validate required fields
    if (!caseFileId) {
      return {
        success: false,
        error: "Case file ID is required",
        data: null,
      };
    }

    // Get case file with relations
    const response = await CaseFileService.getCaseFileById(
      caseFileId,
      includeContacts,
      includeHistory
    );

    if (!response.success || !response.data) {
      return {
        success: false,
        error: response.message || "Failed to get case file",
        data: null,
      };
    }

    return {
      success: true,
      error: "",
      data: response.data,
    };
  } catch (error) {
    logger.error(`Unexpected error getting case file: ${error}`);
    return {
      success: false,
      error: `Unexpected error getting case file: ${error}`,
      data: null,
    };
  }
});

/**
 * Get case file statistics for organization dashboard
 * @returns ActionState with case file statistics
 */
export const getCaseFileStatistics = requirePermission(DOMAIN_PERMISSIONS.VIEW)(
  async (): Promise<
    ActionState<{
      opening: number;
      active: number;
      suspended: number;
      closed: number;
      total: number;
    } | null>
  > => {
    try {
      // Get statistics from aggregation service
      const response = await CaseFileAggregationService.getCaseFileStatistics();

      if (!response.success || !response.data) {
        return {
          success: false,
          error: response.message || "Failed to get case file statistics",
          data: null,
        };
      }

      return {
        success: true,
        error: "",
        data: response.data,
      };
    } catch (error) {
      logger.error(`Unexpected error getting case file statistics: ${error}`);
      return {
        success: false,
        error: `Unexpected error getting case file statistics: ${error}`,
        data: null,
      };
    }
  }
);

/**
 * Get active case files for dashboard
 * @returns ActionState with active case files
 */
export const getActiveCaseFiles = requirePermission(DOMAIN_PERMISSIONS.VIEW)(
  async (): Promise<ActionState<CaseFileSummary[] | null>> => {
    try {
      // Get active case files from service
      const response = await CaseFileService.getActiveCaseFiles();

      if (!response.success || !response.data) {
        return {
          success: false,
          error: response.message || "Failed to get active case files",
          data: null,
        };
      }

      // Convert to summaries for dashboard display
      const summaries: CaseFileSummary[] = response.data.map((caseFile) => ({
        id: caseFile.id,
        case_number: caseFile.case_number,
        status: caseFile.status,
        created_at: caseFile.created_at,
        activated_at: caseFile.activated_at,
        suspended_at: caseFile.suspended_at,
        closed_at: caseFile.closed_at,
        assigned_to: caseFile.assigned_to,
        primaryContactName: undefined, // Will be populated by individual summary calls if needed
        totalContacts: 0, // Will be populated by individual summary calls if needed
        upcomingAppointments: 0, // Will be populated when scheduling features are added
        pendingDocuments: 0, // Will be populated when document features are added
      }));

      return {
        success: true,
        error: "",
        data: summaries,
      };
    } catch (error) {
      logger.error(`Unexpected error getting active case files: ${error}`);
      return {
        success: false,
        error: `Unexpected error getting active case files: ${error}`,
        data: null,
      };
    }
  }
);

/**
 * Get case file status for routing decisions
 * @param caseFileId The ID of the case file
 * @returns ActionState with case file status
 */
export const getCaseFileStatus = requirePermission(DOMAIN_PERMISSIONS.VIEW)(async (
  caseFileId: string
): Promise<ActionState<{ status: CaseFileStatus } | null>> => {
  try {
    // Validate required fields
    if (!caseFileId) {
      return {
        success: false,
        error: "Case file ID is required",
        data: null,
      };
    }

    // Get case file from service
    const response = await CaseFileService.getCaseFileById(caseFileId, false, false);

    if (!response.success || !response.data) {
      return {
        success: false,
        error: response.message || "Case file not found",
        data: null,
      };
    }

    return {
      success: true,
      error: "",
      data: { status: response.data.status },
    };
  } catch (error) {
    logger.error(`Unexpected error getting case file status: ${error}`);
    return {
      success: false,
      error: `Unexpected error getting case file status: ${error}`,
      data: null,
    };
  }
});
