import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { H1, P } from "@/components/typography";
import { SearchBar } from "@/components/SearchBar";
import { Pagination } from "@/components/Pagination";
import { Eye, Calendar, Users, Search, Plus } from "lucide-react";
import Link from "next/link";
import { listCaseFileSummaries } from "../actions";
import { PageHeader } from "@/components";
import { StatusFilter } from "../components/StatusFilter";

interface CaseFileListPageProps {
  params: Promise<{
    lang: string;
  }>;
  searchParams: Promise<{
    search?: string;
    status?: string;
    page?: string;
  }>;
}

/**
 * Case File List Page
 * Displays all case files with search and filtering capabilities
 */
export default async function CaseFileListPage({ params, searchParams }: CaseFileListPageProps) {
  const { lang } = await params;
  const { search, status, page } = await searchParams;

  // Get case file summaries with current filters
  const caseFilesResponse = await listCaseFileSummaries({
    search: search || undefined,
    status: status as "opening" | "active" | "suspended" | "closed" | undefined,
    page: parseInt(page || "1"),
    limit: 20,
    sortBy: "updated_at",
    sortOrder: "desc",
  });

  // Server action guarantees data structure, so we can safely destructure
  const caseFiles = caseFilesResponse.data?.items ?? [];
  const total = caseFilesResponse.data?.total ?? 0;

  return (
    <div className="space-y-6">
      <PageHeader title="Case Files" description="Manage and view all case files" />

      {/* Search and Filters */}
      <div className="flex gap-4 items-end">
        <div className="flex-1">
          <SearchBar
            lang={lang}
            placeholder="Search by case number or contact name..."
            basePath={`/${lang}/protected/case-file/list`}
          />
        </div>
        <div className="mb-6">
          <StatusFilter lang={lang} />
        </div>
      </div>

      {/* Case Files Table */}
      <Card>
        <CardHeader>
          <CardTitle>Case Files ({total} total)</CardTitle>
        </CardHeader>
        <CardContent>
          {caseFiles.length > 0 ? (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Case Number</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Primary Contact</TableHead>
                  <TableHead>Total Contacts</TableHead>
                  <TableHead>Created</TableHead>
                  <TableHead>Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {caseFiles.map((caseFile) => (
                  <TableRow key={caseFile.id}>
                    <TableCell className="font-medium">{caseFile.case_number}</TableCell>
                    <TableCell>
                      <Badge
                        variant={
                          caseFile.status === "active"
                            ? "default"
                            : caseFile.status === "opening"
                              ? "secondary"
                              : caseFile.status === "suspended"
                                ? "outline"
                                : "destructive"
                        }
                      >
                        {caseFile.status}
                      </Badge>
                    </TableCell>
                    <TableCell>{caseFile.primaryContactName || "No primary contact"}</TableCell>
                    <TableCell>
                      <div className="flex items-center">
                        <Users className="h-4 w-4 mr-1" />
                        {caseFile.totalContacts}
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center">
                        <Calendar className="h-4 w-4 mr-1" />
                        {new Date(caseFile.created_at).toLocaleDateString()}
                      </div>
                    </TableCell>
                    <TableCell>
                      <Button asChild variant="outline" size="sm">
                        <Link
                          href={`/${lang}/protected/case-file/${caseFile.id}/${caseFile.status}`}
                        >
                          <Eye className="h-4 w-4 mr-1" />
                          View
                        </Link>
                      </Button>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          ) : (
            <div className="text-center py-12">
              <div className="mx-auto w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mb-4">
                <Search className="h-8 w-8 text-gray-400" />
              </div>
              <H1 className="text-xl font-semibold mb-2">No case files found</H1>
              <P className="text-muted-foreground mb-6">
                {search || status
                  ? "Try adjusting your search criteria or filters"
                  : "Get started by creating your first case file"}
              </P>
              <Button asChild>
                <Link href={`/${lang}/protected/case-file/create`}>
                  <Plus className="h-4 w-4 mr-2" />
                  Create Case File
                </Link>
              </Button>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Pagination */}
      {total > 0 && (
        <Pagination
          totalItems={total}
          pageSize={20}
          currentPage={parseInt(page || "1")}
          lang={lang}
          search={search}
          itemName="case files"
          basePath={`/${lang}/protected/case-file/list`}
        />
      )}
    </div>
  );
}
