import { ReactNode } from "react";
import { getDictionary } from "@/lib/i18n/services/I18nService";
import { Locale } from "@/lib/i18n/settings";
import FEATURE_CONFIG from "./lib/config/feature";

// Using any type to bypass type checking issues
type AvailabilityLayoutProps = any;

export default async function AvailabilityLayout({
  children,
  params: { lang },
}: AvailabilityLayoutProps) {
  const dictionary = await getDictionary(lang);
  const featureDictionary = dictionary.employee.availability;

  return (
    <div className="container mx-auto py-6">
      <div className="mb-6">
        <h1 className="text-3xl font-bold">{featureDictionary.title}</h1>
        <p className="text-muted-foreground">{featureDictionary.description}</p>
      </div>
      {children}
    </div>
  );
}
