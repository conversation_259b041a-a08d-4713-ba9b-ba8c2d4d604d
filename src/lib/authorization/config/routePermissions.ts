import { Permission } from "../types";
import { PERMISSIONS } from "./permissions";
import { MANAGEMENT_ROUTE_PERMISSIONS as EMPLOYEE_MANAGEMENT_ROUTE_PERMISSIONS } from "@/app/[lang]/protected/employee/(features)/management/lib/security/permissions";
import CONTACT_SECURITY from "@/app/[lang]/protected/contact/lib/security";

/**
 * Define the structure for route permission mapping
 * Key is the route pattern, value is the required permission
 */
type RoutePermissionMapping = {
  [routePattern: string]: Permission;
};

/**
 * Define the permissions required for each route
 */
export const routePermissions: RoutePermissionMapping = {
  ...CONTACT_SECURITY.routePermissions,
  // Organization system-admin routes
  "/[lang]/protected/organization/system-admin": PERMISSIONS.ORGANIZATION.SYSTEM_ADMIN.ALL,
  "/[lang]/protected/organization/system-admin/list": PERMISSIONS.ORGANIZATION.SYSTEM_ADMIN.LIST,
  "/[lang]/protected/organization/system-admin/create":
    PERMISSIONS.ORGANIZATION.SYSTEM_ADMIN.CREATE,
  "/[lang]/protected/organization/system-admin/[id]": PERMISSIONS.ORGANIZATION.SYSTEM_ADMIN.READ,
  "/[lang]/protected/organization/system-admin/edit/[id]":
    PERMISSIONS.ORGANIZATION.SYSTEM_ADMIN.UPDATE,

  // Organization profile routes
  "/[lang]/protected/organization/profile": PERMISSIONS.ORGANIZATION.PROFILE.MANAGE,

  // User management routes
  "/[lang]/protected/user/management/list": PERMISSIONS.USER.MANAGEMENT.LIST,

  // Employee management routes
  ...EMPLOYEE_MANAGEMENT_ROUTE_PERMISSIONS,

  // Employee availability routes
  "/[lang]/protected/employee/availability": PERMISSIONS.EMPLOYEE.AVAILABILITY.VIEW,
  "/[lang]/protected/employee/availability/list": PERMISSIONS.EMPLOYEE.AVAILABILITY.VIEW,
  "/[lang]/protected/employee/availability/[id]/view": PERMISSIONS.EMPLOYEE.AVAILABILITY.VIEW,
  "/[lang]/protected/employee/availability/[id]/edit": PERMISSIONS.EMPLOYEE.AVAILABILITY.EDIT,

  // Request routes
  "/[lang]/protected/request": PERMISSIONS.REQUEST.MANAGEMENT.VIEW,
  "/[lang]/protected/request/list": PERMISSIONS.REQUEST.MANAGEMENT.VIEW,
  "/[lang]/protected/request/[id]": PERMISSIONS.REQUEST.MANAGEMENT.VIEW,
  "/[lang]/protected/request/[id]/view": PERMISSIONS.REQUEST.MANAGEMENT.VIEW,

  // Request creation routes
  "/[lang]/protected/request/create": PERMISSIONS.REQUEST.MANAGEMENT.CREATE,
  "/[lang]/protected/request/new": PERMISSIONS.REQUEST.MANAGEMENT.CREATE,

  // Request edit routes
  "/[lang]/protected/request/[id]/edit": PERMISSIONS.REQUEST.MANAGEMENT.UPDATE,
  "/[lang]/protected/request/edit/[id]": PERMISSIONS.REQUEST.MANAGEMENT.UPDATE,

  // Request delete routes
  "/[lang]/protected/request/[id]/delete": PERMISSIONS.REQUEST.MANAGEMENT.DELETE,
  "/[lang]/protected/request/delete/[id]": PERMISSIONS.REQUEST.MANAGEMENT.DELETE,

  // Request workflow routes
  "/[lang]/protected/request/[id]/workflow": PERMISSIONS.REQUEST.MANAGEMENT.WORKFLOW,
  "/[lang]/protected/request/workflow/[id]": PERMISSIONS.REQUEST.MANAGEMENT.WORKFLOW,
};
