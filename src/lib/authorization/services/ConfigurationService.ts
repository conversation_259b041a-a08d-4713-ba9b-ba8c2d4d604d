import { logger } from "@/lib/logger/services/LoggerService";
import { Permission, UserRole } from "../types";
import { PERMISSIONS } from "../config/permissions";

/**
 * Type for role permission configuration
 */
type RolePermissionConfig = Record<UserRole, Permission[]>;

/**
 * Service for managing authorization configuration
 */
export class ConfigurationService {
  /**
   * Default role permission configuration
   */
  private static config: RolePermissionConfig = {
    // Director has full access to their organization but not system-admin features
    Director: [
      PERMISSIONS.CONTACT.MANAGEMENT.ALL,
      // User domain - only their own profile
      PERMISSIONS.USER.PROFILE.ALL,
      PERMISSIONS.USER.MANAGEMENT.ALL,

      // Organization domain - everything except system-admin
      PERMISSIONS.ORGANIZATION.PROFILE.ALL,
      PERMISSIONS.EMPLOYEE.MANAGEMENT.ALL,
      PERMISSIONS.EMPLOYEE.AVAILABILITY.VIEW,
      PERMISSIONS.EMPLOYEE.AVAILABILITY.EDIT,

      // Request domain - full access
      PERMISSIONS.REQUEST.MANAGEMENT.VIEW,
      PERMISSIONS.REQUEST.MANAGEMENT.CREATE,
      PERMISSIONS.REQUEST.MANAGEMENT.UPDATE,
      PERMISSIONS.REQUEST.MANAGEMENT.DELETE,
      PERMISSIONS.REQUEST.MANAGEMENT.WORKFLOW,

      // Case File domain - full access
      PERMISSIONS.CASE_FILE.VIEW,
      PERMISSIONS.CASE_FILE.CREATE,
      PERMISSIONS.CASE_FILE.UPDATE,
      PERMISSIONS.CASE_FILE.DELETE,
      PERMISSIONS.CASE_FILE.ACTIVATE,
      PERMISSIONS.CASE_FILE.SUSPEND,
      PERMISSIONS.CASE_FILE.CLOSE,
      PERMISSIONS.CASE_FILE.ADMIN,

      PERMISSIONS.ALL,
    ],

    // Coordinator has limited access to organization features
    Coordinator: [
      // Contact domain - full access
      PERMISSIONS.CONTACT.MANAGEMENT.ALL,

      // User domain - only their own profile
      PERMISSIONS.USER.PROFILE.ALL,

      // Organization domain - read access
      PERMISSIONS.ORGANIZATION.PROFILE.ALL,

      // Request domain - view, create, update, and workflow access
      PERMISSIONS.REQUEST.MANAGEMENT.VIEW,
      PERMISSIONS.REQUEST.MANAGEMENT.CREATE,
      PERMISSIONS.REQUEST.MANAGEMENT.UPDATE,
      PERMISSIONS.REQUEST.MANAGEMENT.WORKFLOW,

      // Case File domain - view, create, update, and state management access
      PERMISSIONS.CASE_FILE.VIEW,
      PERMISSIONS.CASE_FILE.CREATE,
      PERMISSIONS.CASE_FILE.UPDATE,
      PERMISSIONS.CASE_FILE.ACTIVATE,
      PERMISSIONS.CASE_FILE.SUSPEND,
      PERMISSIONS.CASE_FILE.CLOSE,
    ],

    // SocialWorker has minimal access focused on their own work
    SocialWorker: [
      // Contact domain - full access
      PERMISSIONS.CONTACT.MANAGEMENT.ALL,

      // User domain - only their own profile
      PERMISSIONS.USER.PROFILE.ALL,

      // Organization domain - read-only access
      PERMISSIONS.ORGANIZATION.PROFILE.READ,

      // Request domain - view access
      PERMISSIONS.REQUEST.MANAGEMENT.VIEW,

      // Case File domain - view access
      PERMISSIONS.CASE_FILE.VIEW,
    ],

    // SystemAdmin has full access to everything
    SystemAdmin: [
      // Global wildcard - full access to everything
      PERMISSIONS.ALL,
    ],
  };

  /**
   * Get the permissions for a role
   * @param role The role
   * @returns The permissions for the role
   */
  static getPermissionsForRole(role: UserRole): Permission[] {
    return this.config[role] || [];
  }

  // Permission management methods removed as they are out of scope for MVP

  /**
   * Reset the configuration to the default
   */
  static resetConfiguration(): void {
    logger.info("Resetting authorization configuration to default");
    this.config = {
      // Director has full access to their organization but not system-admin features
      Director: [
        // Contact domain - full access
        PERMISSIONS.CONTACT.MANAGEMENT.ALL,

        // User domain - only their own profile
        PERMISSIONS.USER.PROFILE.ALL,

        // Organization domain - everything except system-admin
        PERMISSIONS.ORGANIZATION.PROFILE.ALL,
        PERMISSIONS.EMPLOYEE.MANAGEMENT.ALL,
        PERMISSIONS.EMPLOYEE.AVAILABILITY.VIEW,
        PERMISSIONS.EMPLOYEE.AVAILABILITY.EDIT,

        // Request domain - full access
        PERMISSIONS.REQUEST.MANAGEMENT.VIEW,
        PERMISSIONS.REQUEST.MANAGEMENT.CREATE,
        PERMISSIONS.REQUEST.MANAGEMENT.UPDATE,
        PERMISSIONS.REQUEST.MANAGEMENT.DELETE,
        PERMISSIONS.REQUEST.MANAGEMENT.WORKFLOW,
      ],

      // Coordinator has limited access to organization features
      Coordinator: [
        // Contact domain - full access
        PERMISSIONS.CONTACT.MANAGEMENT.ALL,

        // User domain - only their own profile
        PERMISSIONS.USER.PROFILE.ALL,

        // Organization domain - read access
        PERMISSIONS.ORGANIZATION.PROFILE.ALL,

        // Request domain - view, create, update, and workflow access
        PERMISSIONS.REQUEST.MANAGEMENT.VIEW,
        PERMISSIONS.REQUEST.MANAGEMENT.CREATE,
        PERMISSIONS.REQUEST.MANAGEMENT.UPDATE,
        PERMISSIONS.REQUEST.MANAGEMENT.WORKFLOW,
      ],

      // SocialWorker has minimal access focused on their own work
      SocialWorker: [
        // Contact domain - full access
        PERMISSIONS.CONTACT.MANAGEMENT.ALL,

        // User domain - only their own profile
        PERMISSIONS.USER.PROFILE.ALL,

        // Organization domain - read-only access
        PERMISSIONS.ORGANIZATION.PROFILE.READ,

        // Request domain - view access
        PERMISSIONS.REQUEST.MANAGEMENT.VIEW,
      ],

      // SystemAdmin has full access to everything
      SystemAdmin: [
        // Global wildcard - full access to everything
        PERMISSIONS.ALL,
      ],
    };
  }
}
