"use client";
import React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";

export type HistoryAction = "create" | "update" | "delete";

export interface HistoryChange {
  field: string;
  oldValue?: string | null;
  newValue?: string | null;
}

export interface HistoryItem {
  id: string;
  timestamp: string | Date;
  user: {
    id: string;
    name: string;
    avatarUrl?: string;
  };
  action: HistoryAction;
  changes?: HistoryChange[];
  summary?: string;
}

interface HistoryTimelineProps {
  /**
   * The history items to display
   */
  items: HistoryItem[];

  /**
   * The title of the timeline
   */
  title?: string;

  /**
   * Whether to show the title
   */
  showTitle?: boolean;

  /**
   * Additional CSS classes
   */
  className?: string;
}

/**
 * History timeline component for displaying entity change history
 *
 * @example
 * ```tsx
 * <HistoryTimeline
 *   title="Contact History"
 *   items={[
 *     {
 *       id: "1",
 *       timestamp: "2023-01-01T12:00:00Z",
 *       user: {
 *         id: "user1",
 *         name: "<PERSON>",
 *         avatarUrl: "/avatars/john.jpg"
 *       },
 *       action: "create",
 *       summary: "Contact created"
 *     },
 *     {
 *       id: "2",
 *       timestamp: "2023-01-02T14:30:00Z",
 *       user: {
 *         id: "user2",
 *         name: "Jane Smith",
 *         avatarUrl: "/avatars/jane.jpg"
 *       },
 *       action: "update",
 *       changes: [
 *         { field: "name", oldValue: "John Smith", newValue: "John Doe" },
 *         { field: "email", oldValue: "<EMAIL>", newValue: "<EMAIL>" }
 *       ]
 *     }
 *   ]}
 * />
 * ```
 */
export function HistoryTimeline({
  items,
  title = "History",
  showTitle = true,
  className = "",
}: HistoryTimelineProps) {
  // Ensure items is an array and sort by timestamp (newest first)
  const itemsArray = Array.isArray(items) ? items : [];
  const sortedItems = [...itemsArray].sort((a, b) => {
    const dateA = new Date(a.timestamp);
    const dateB = new Date(b.timestamp);
    return dateB.getTime() - dateA.getTime();
  });

  return (
    <Card className={className}>
      {showTitle && (
        <CardHeader className="pb-2">
          <CardTitle className="text-lg">{title}</CardTitle>
        </CardHeader>
      )}
      <CardContent className="p-4">
        <div className="space-y-4 w-full">
          {sortedItems.length === 0 ? (
            <p className="text-center text-muted-foreground py-2">No history available</p>
          ) : (
            sortedItems.map((item) => <HistoryTimelineItem key={item.id} item={item} />)
          )}
        </div>
      </CardContent>
    </Card>
  );
}

interface HistoryTimelineItemProps {
  /**
   * The history item to display
   */
  item: HistoryItem;
}

function HistoryTimelineItem({ item }: HistoryTimelineItemProps) {
  // Format the timestamp to a more natural format
  const formatDate = (timestamp: string | Date): string => {
    try {
      const date = new Date(timestamp);
      return date.toLocaleDateString("en-US", {
        month: "short",
        day: "numeric",
        year: "numeric",
        hour: "2-digit",
        minute: "2-digit",
      });
    } catch (error) {
      return String(timestamp);
    }
  };

  // Get the initials for the avatar fallback
  const getInitials = (name: string) => {
    return name
      .split(" ")
      .map((n) => n[0])
      .join("")
      .toUpperCase();
  };

  return (
    <div className="relative pl-12 border-l-2 border-muted pb-4 last:pb-0">
      {/* Timeline dot */}
      <div className="absolute -left-[7px] top-1 w-3 h-3 rounded-full bg-background border-2 border-primary" />

      {/* Avatar */}
      <div className="absolute left-3 top-0">
        <Avatar className="h-8 w-8">
          {item.user.avatarUrl ? (
            <AvatarImage src={item.user.avatarUrl} alt={item.user.name} />
          ) : (
            <AvatarFallback className="text-xs">{getInitials(item.user.name)}</AvatarFallback>
          )}
        </Avatar>
      </div>

      {/* Content */}
      <div className="ml-2">
        {/* Full name */}
        <div className="font-medium text-sm text-foreground">{item.user.name}</div>

        {/* Date */}
        <div className="text-xs text-muted-foreground mt-0.5">{formatDate(item.timestamp)}</div>

        {/* Action */}
        <Badge variant="default">{item.action}</Badge>
      </div>
    </div>
  );
}
