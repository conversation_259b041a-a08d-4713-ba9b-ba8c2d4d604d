"use client";
import React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTit<PERSON> } from "@/components/ui/card";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "@/components/ui/collapsible";
import { ChevronDown, ChevronUp, Clock } from "lucide-react";
import { cn } from "@/lib/utils";

export type HistoryAction = "create" | "update" | "delete";

export interface HistoryChange {
  field: string;
  oldValue?: string | null;
  newValue?: string | null;
}

export interface HistoryItem {
  id: string;
  timestamp: string | Date;
  user: {
    id: string;
    name: string;
    avatarUrl?: string;
  };
  action: HistoryAction;
  changes?: HistoryChange[];
  summary?: string;
}

interface HistoryTimelineProps {
  /**
   * The history items to display
   */
  items: HistoryItem[];

  /**
   * The title of the timeline
   */
  title?: string;

  /**
   * Whether to show the title
   */
  showTitle?: boolean;

  /**
   * Additional CSS classes
   */
  className?: string;
}

/**
 * History timeline component for displaying entity change history
 *
 * @example
 * ```tsx
 * <HistoryTimeline
 *   title="Contact History"
 *   items={[
 *     {
 *       id: "1",
 *       timestamp: "2023-01-01T12:00:00Z",
 *       user: {
 *         id: "user1",
 *         name: "John Doe",
 *         avatarUrl: "/avatars/john.jpg"
 *       },
 *       action: "create",
 *       summary: "Contact created"
 *     },
 *     {
 *       id: "2",
 *       timestamp: "2023-01-02T14:30:00Z",
 *       user: {
 *         id: "user2",
 *         name: "Jane Smith",
 *         avatarUrl: "/avatars/jane.jpg"
 *       },
 *       action: "update",
 *       changes: [
 *         { field: "name", oldValue: "John Smith", newValue: "John Doe" },
 *         { field: "email", oldValue: "<EMAIL>", newValue: "<EMAIL>" }
 *       ]
 *     }
 *   ]}
 * />
 * ```
 */
export function HistoryTimeline({
  items,
  title = "History",
  showTitle = true,
  className = "",
}: HistoryTimelineProps) {
  // Ensure items is an array and sort by timestamp (newest first)
  const itemsArray = Array.isArray(items) ? items : [];
  const sortedItems = [...itemsArray].sort((a, b) => {
    const dateA = new Date(a.timestamp);
    const dateB = new Date(b.timestamp);
    return dateB.getTime() - dateA.getTime();
  });

  return (
    <Card className={className}>
      {showTitle && (
        <CardHeader className="pb-2">
          <CardTitle className="text-lg">{title}</CardTitle>
        </CardHeader>
      )}
      <CardContent className="p-4">
        <div className="space-y-4 w-full">
          {sortedItems.length === 0 ? (
            <p className="text-center text-muted-foreground py-2">No history available</p>
          ) : (
            sortedItems.map((item) => <HistoryTimelineItem key={item.id} item={item} />)
          )}
        </div>
      </CardContent>
    </Card>
  );
}

interface HistoryTimelineItemProps {
  /**
   * The history item to display
   */
  item: HistoryItem;
}

function HistoryTimelineItem({ item }: HistoryTimelineItemProps) {
  const [isOpen, setIsOpen] = React.useState(false);

  // Format the timestamp with consistent output for server and client
  const formatDate = (timestamp: string | Date): string => {
    try {
      const date = new Date(timestamp);
      // Use explicit formatting to ensure consistency between server and client
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, "0");
      const day = String(date.getDate()).padStart(2, "0");
      const hours = String(date.getHours()).padStart(2, "0");
      const minutes = String(date.getMinutes()).padStart(2, "0");
      const seconds = String(date.getSeconds()).padStart(2, "0");

      return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
    } catch (error) {
      return String(timestamp);
    }
  };

  const formattedDate = formatDate(item.timestamp);

  // Get the initials for the avatar fallback
  const getInitials = (name: string) => {
    return name
      .split(" ")
      .map((n) => n[0])
      .join("")
      .toUpperCase();
  };

  // Get the badge variant based on the action
  const getBadgeVariant = (action: HistoryAction) => {
    switch (action) {
      case "create":
        return "default";
      case "update":
        return "secondary";
      case "delete":
        return "destructive";
      default:
        return "outline";
    }
  };

  // Get the action text
  const getActionText = (action: HistoryAction) => {
    switch (action) {
      case "create":
        return "Created";
      case "update":
        return "Updated";
      case "delete":
        return "Deleted";
      default:
        return action;
    }
  };

  return (
    <div className="relative pl-5 border-l-2 border-muted pb-3 last:pb-0">
      <div className="absolute -left-[7px] top-0 w-3 h-3 rounded-full bg-background border-2 border-primary" />

      <div className="flex items-start gap-2">
        <Avatar className="h-7 w-7">
          {item.user.avatarUrl ? (
            <AvatarImage src={item.user.avatarUrl} alt={item.user.name} />
          ) : (
            <AvatarFallback>{getInitials(item.user.name)}</AvatarFallback>
          )}
        </Avatar>

        <div className="flex-1 min-w-0">
          <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-1">
            <div className="flex items-center flex-wrap gap-1">
              <span className="font-medium text-sm break-words">{item.user.name}</span>
              <Badge variant={getBadgeVariant(item.action)} className="text-xs px-1.5 py-0">
                {getActionText(item.action)}
              </Badge>
            </div>
            <div className="flex items-center text-xs text-muted-foreground">
              <Clock className="mr-1 h-3 w-3 flex-shrink-0" />
              <span className="break-words">{formattedDate}</span>
            </div>
          </div>

          {item.summary && <p className="mt-0.5 text-xs break-words">{item.summary}</p>}

          {item.changes && item.changes.length > 0 && (
            <Collapsible open={isOpen} onOpenChange={setIsOpen} className="mt-1">
              <CollapsibleTrigger className="flex items-center text-xs text-primary hover:underline">
                {isOpen ? (
                  <>
                    <ChevronUp className="mr-1 h-3 w-3" />
                    Hide changes
                  </>
                ) : (
                  <>
                    <ChevronDown className="mr-1 h-3 w-3" />
                    View changes
                  </>
                )}
              </CollapsibleTrigger>
              <CollapsibleContent className="mt-1">
                <div className="rounded-md border p-1 text-xs">
                  <table className="w-full table-fixed">
                    <thead>
                      <tr className="border-b">
                        <th className="text-left py-0.5 px-1 font-medium w-1/4">Field</th>
                        <th className="text-left py-0.5 px-1 font-medium w-1/4">Old Value</th>
                        <th className="text-left py-0.5 px-1 font-medium w-2/4">New Value</th>
                      </tr>
                    </thead>
                    <tbody>
                      {item.changes.map((change, index) => (
                        <tr
                          key={index}
                          className={cn(
                            "border-b last:border-0",
                            index % 2 === 0 ? "bg-muted/50" : ""
                          )}
                        >
                          <td className="py-0.5 px-1 break-words">{change.field}</td>
                          <td className="py-0.5 px-1 text-muted-foreground break-words">
                            {change.oldValue || "-"}
                          </td>
                          <td className="py-0.5 px-1 break-words">{change.newValue || "-"}</td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </CollapsibleContent>
            </Collapsible>
          )}
        </div>
      </div>
    </div>
  );
}
