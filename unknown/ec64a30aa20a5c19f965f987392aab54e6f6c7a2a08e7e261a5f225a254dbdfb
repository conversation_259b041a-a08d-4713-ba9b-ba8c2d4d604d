"use server";

import { logger } from "@/lib/logger/services/LoggerService";
import {
  DocumentAttachmentService,
  AttachmentListParams,
} from "../../../lib/services/DocumentAttachmentService";
import { FileValidationService } from "../../../lib/services/FileValidationService";
import { ActionState } from "@/lib/types/responses";
import { Database } from "@/lib/types/database.types";
import { auth } from "@/lib/authentication/services/AuthenticationService";
import { PermissionService } from "@/lib/authorization/services/PermissionService";
import { DOCUMENT_PERMISSIONS } from "@/app/[lang]/protected/document/lib/security";
import { revalidatePath } from "next/cache";

type DocumentAttachment = Database["public"]["Tables"]["document_attachments"]["Row"];

/**
 * List document attachments with filtering and pagination
 */
export async function getAttachments(
  params: AttachmentListParams = {}
): Promise<ActionState<{ items: DocumentAttachment[]; total: number }>> {
  try {
    // Validate user authentication
    const user = await auth.getCurrentUser();
    if (!user) {
      return {
        success: false,
        error: "Authentication required",
        data: { items: [], total: 0 },
      };
    }

    // Check authorization
    const role = await auth.getCurrentUserRole();
    if (!role) {
      return {
        success: false,
        error: "Authentication required",
        data: { items: [], total: 0 },
      };
    }
    const hasPermission = PermissionService.hasPermission(
      role,
      DOCUMENT_PERMISSIONS.ATTACHMENT.VIEW
    );
    if (!hasPermission) {
      return {
        success: false,
        error: "Insufficient permissions",
        data: { items: [], total: 0 },
      };
    }

    // Set default values for pagination if not provided
    const listParams: AttachmentListParams = {
      page: params.page || 1,
      limit: params.limit || 12,
      sortBy: params.sortBy || "uploaded_at",
      sortOrder: params.sortOrder || "desc",
      ...params,
    };

    // Validate pagination parameters
    if (listParams.page && listParams.page < 1) {
      return {
        success: false,
        error: "Page number must be at least 1",
        data: { items: [], total: 0 },
      };
    }

    if (listParams.limit && (listParams.limit < 1 || listParams.limit > 50)) {
      return {
        success: false,
        error: "Page size must be between 1 and 50",
        data: { items: [], total: 0 },
      };
    }

    // Call the service to get the attachments
    const response = await DocumentAttachmentService.list(listParams);

    if (!response.success || !response.data) {
      const errorMessage =
        typeof response.error === "string"
          ? response.error
          : response.error?.message || "Failed to fetch attachments";
      return {
        success: false,
        error: errorMessage,
        data: { items: [], total: 0 },
      };
    }

    logger.info(`Listed ${response.data.items.length} attachments for user ${user.id}`);

    return {
      success: true,
      error: "",
      data: response.data,
    };
  } catch (error) {
    logger.error("Error in getAttachments action:", error as Error);
    return {
      success: false,
      error: "Failed to fetch attachments",
      data: { items: [], total: 0 },
    };
  }
}

/**
 * Get attachments for a specific entity
 */
export async function getEntityAttachments(
  entityType: string,
  entityId: string
): Promise<ActionState<DocumentAttachment[]>> {
  try {
    // Validate user authentication
    const user = await auth.getCurrentUser();
    if (!user) {
      return {
        success: false,
        error: "Authentication required",
        data: [],
      };
    }

    // Validate parameters
    if (!entityType || !entityId) {
      return {
        success: false,
        error: "Entity type and ID are required",
        data: [],
      };
    }

    // Call the service to get entity attachments
    const response = await DocumentAttachmentService.getByEntity(entityType, entityId);

    if (!response.success || !response.data) {
      const errorMessage =
        typeof response.error === "string"
          ? response.error
          : response.error?.message || "Failed to fetch entity attachments";
      return {
        success: false,
        error: errorMessage,
        data: [],
      };
    }

    logger.info(`Listed ${response.data.length} attachments for ${entityType}:${entityId}`);

    return {
      success: true,
      error: "",
      data: response.data,
    };
  } catch (error) {
    logger.error("Error in getEntityAttachments action:", error as Error);
    return {
      success: false,
      error: "Failed to fetch entity attachments",
      data: [],
    };
  }
}

/**
 * Get a single attachment by ID
 */
export async function getAttachmentById(id: string): Promise<ActionState<DocumentAttachment>> {
  try {
    // Validate user authentication
    const user = await auth.getCurrentUser();
    if (!user) {
      return {
        success: false,
        error: "Authentication required",
        data: null,
      };
    }

    // Validate parameters
    if (!id) {
      return {
        success: false,
        error: "Attachment ID is required",
        data: null,
      };
    }

    // Call the service to get the attachment
    const response = await DocumentAttachmentService.getById(id);

    if (!response.success || !response.data) {
      const errorMessage =
        typeof response.error === "string"
          ? response.error
          : response.error?.message || "Attachment not found";
      return {
        success: false,
        error: errorMessage,
        data: null,
      };
    }

    return {
      success: true,
      error: "",
      data: response.data,
    };
  } catch (error) {
    logger.error("Error in getAttachmentById action:", error as Error);
    return {
      success: false,
      error: "Failed to fetch attachment",
      data: null,
    };
  }
}

// REMOVED: uploadAttachment function - File uploads moved to API routes

/**
 * Delete an attachment
 */
export async function deleteAttachment(id: string): Promise<ActionState<boolean>> {
  try {
    // Validate user authentication
    const user = await auth.getCurrentUser();
    if (!user) {
      return {
        success: false,
        error: "Authentication required",
        data: false,
      };
    }

    // Validate parameters
    if (!id) {
      return {
        success: false,
        error: "Attachment ID is required",
        data: false,
      };
    }

    // Call the service to delete the attachment
    const response = await DocumentAttachmentService.deleteAttachment(id);

    if (!response.success) {
      const errorMessage =
        typeof response.error === "string"
          ? response.error
          : response.error?.message || "Failed to delete attachment";
      return {
        success: false,
        error: errorMessage,
        data: false,
      };
    }

    // Revalidate relevant paths
    revalidatePath("/protected/document/attachments");

    logger.info(`Attachment deleted: ${id} by user ${user.id}`);

    return {
      success: true,
      error: "",
      data: true,
    };
  } catch (error) {
    logger.error("Error in deleteAttachment action:", error as Error);
    return {
      success: false,
      error: "Failed to delete attachment",
      data: false,
    };
  }
}

/**
 * Update attachment metadata
 */
export async function updateAttachmentMetadata(
  id: string,
  formData: FormData
): Promise<ActionState<DocumentAttachment>> {
  try {
    // Validate user authentication
    const user = await auth.getCurrentUser();
    if (!user) {
      return {
        success: false,
        error: "Authentication required",
        data: null,
      };
    }

    // Validate parameters
    if (!id) {
      return {
        success: false,
        error: "Attachment ID is required",
        data: null,
      };
    }

    // Extract form data
    const category = formData.get("category") as string;
    const tagsString = formData.get("tags") as string;
    const description = formData.get("description") as string;

    // Parse tags
    const tags = tagsString
      ? tagsString
          .split(",")
          .map((tag) => tag.trim())
          .filter(Boolean)
      : [];

    // Prepare metadata update
    const metadata: { category?: string; tags?: string[]; description?: string } = {};
    if (category) metadata.category = category;
    if (tags.length > 0) metadata.tags = tags;
    if (description) metadata.description = description;

    // Call the service to update metadata
    const response = await DocumentAttachmentService.updateMetadata(id, metadata);

    if (!response.success || !response.data) {
      const errorMessage =
        typeof response.error === "string"
          ? response.error
          : response.error?.message || "Failed to update attachment metadata";
      return {
        success: false,
        error: errorMessage,
        data: null,
      };
    }

    // Revalidate relevant paths
    revalidatePath("/protected/document/attachments");
    revalidatePath(`/protected/document/attachments/${id}`);

    logger.info(`Attachment metadata updated: ${id} by user ${user.id}`);

    return {
      success: true,
      error: "",
      data: response.data,
    };
  } catch (error) {
    logger.error("Error in updateAttachmentMetadata action:", error as Error);
    return {
      success: false,
      error: "Failed to update attachment metadata",
      data: null,
    };
  }
}
