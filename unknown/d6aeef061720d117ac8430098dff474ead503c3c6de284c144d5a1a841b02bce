{"name": "rqrsda2025", "version": "0.1.0", "private": true, "scripts": {"dev": "prettier --write \"src/**/*.{ts,tsx}\" && next dev", "build2": "tsc --noEmit && next lint", "build": "prettier --write \"src/**/*.{ts,tsx}\" && next build", "start": "next start", "lint": "next lint", "lint:fix": "next lint --fix", "format": "prettier --write .", "type-check": "tsc --noEmit", "validate": "npm run lint && npm run type-check", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage --coverageReporters=json-summary --coverageReporters=text --coverageReporters=lcov", "setup:local": "bash scripts/setup-local-env/setup-local-env.sh", "test:setup": "bash scripts/setup-local-env/run-tests.sh", "test:shellcheck": "bash scripts/setup-local-env/run-shellcheck.sh", "supabase:start": "npx supabase start", "supabase:stop": "npx supabase stop", "db:reset": "./scripts/db/reset/run.sh", "db:seed": "./scripts/db/seed/run.sh", "db:seed:cloud": "./scripts/db/seed/run.sh --env=cloud", "db:test": "npx supabase test db", "db:migration:new": "npx supabase migration new", "db:migration:up": "npx supabase migration up", "cloud:db:migration:up": "npx supabase migration up --linked", "test:rls": "./scripts/test-rls.sh", "gen:types": "npx supabase gen types typescript --local > src/lib/types/database.types.ts", "n8n:start": "cd docker/n8n && ./n8n.sh start", "n8n:stop": "cd docker/n8n && ./n8n.sh stop", "n8n:restart": "cd docker/n8n && ./n8n.sh restart", "n8n:status": "cd docker/n8n && ./n8n.sh status", "n8n:logs": "cd docker/n8n && ./n8n.sh logs"}, "dependencies": {"@brainstack/log": "^1.1.163", "@cyntler/react-doc-viewer": "^1.17.0", "@radix-ui/react-alert-dialog": "^1.1.13", "@radix-ui/react-avatar": "^1.1.9", "@radix-ui/react-checkbox": "^1.3.1", "@radix-ui/react-collapsible": "^1.1.10", "@radix-ui/react-dialog": "^1.1.13", "@radix-ui/react-dropdown-menu": "^2.1.12", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-label": "^2.1.6", "@radix-ui/react-popover": "^1.1.13", "@radix-ui/react-progress": "^1.1.6", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-scroll-area": "^1.2.8", "@radix-ui/react-select": "^2.2.4", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.2", "@radix-ui/react-switch": "^1.2.4", "@radix-ui/react-tabs": "^1.1.11", "@radix-ui/react-toggle": "^1.1.6", "@radix-ui/react-tooltip": "^1.2.6", "@supabase/auth-helpers-nextjs": "^0.10.0", "@supabase/ssr": "^0.6.1", "@tabler/icons-react": "^3.31.0", "@tailwindcss/postcss": "^4.1.5", "@tiptap/extension-blockquote": "^2.12.0", "@tiptap/extension-bold": "^2.12.0", "@tiptap/extension-bullet-list": "^2.12.0", "@tiptap/extension-code": "^2.12.0", "@tiptap/extension-code-block": "^2.12.0", "@tiptap/extension-heading": "^2.12.0", "@tiptap/extension-highlight": "^2.12.0", "@tiptap/extension-image": "^2.12.0", "@tiptap/extension-italic": "^2.12.0", "@tiptap/extension-link": "^2.12.0", "@tiptap/extension-list-item": "^2.12.0", "@tiptap/extension-ordered-list": "^2.12.0", "@tiptap/extension-placeholder": "^2.12.0", "@tiptap/extension-strike": "^2.12.0", "@tiptap/extension-table": "^2.12.0", "@tiptap/extension-table-cell": "^2.12.0", "@tiptap/extension-table-header": "^2.12.0", "@tiptap/extension-table-row": "^2.12.0", "@tiptap/extension-underline": "^2.12.0", "@tiptap/pm": "^2.12.0", "@tiptap/react": "^2.12.0", "@tiptap/starter-kit": "^2.12.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "date-fns": "^4.1.0", "framer-motion": "^12.12.1", "lucide-react": "^0.507.0", "next": "15.3.1", "next-intl": "^4.0.2", "next-themes": "^0.4.6", "postcss": "^8.5.3", "react": "^19.0.0", "react-day-picker": "^8.10.1", "react-doc-viewer": "^0.1.14", "react-dom": "^19.0.0", "sonner": "^2.0.3", "tailwind-merge": "^3.2.0", "tailwindcss": "^4.1.5", "use-debounce": "^10.0.4", "vaul": "^1.1.2", "zod": "^3.24.4"}, "devDependencies": {"@eslint/eslintrc": "^3", "@supabase/supabase-js": "^2.39.8", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@types/jest": "^29.5.14", "@types/jest-axe": "^3.5.9", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "dotenv": "^16.4.5", "eslint": "^9", "eslint-config-next": "15.3.1", "jest": "^29.7.0", "jest-axe": "^10.0.0", "jest-environment-jsdom": "^29.7.0", "prettier": "^3.5.3", "ts-node": "^10.9.2", "tw-animate-css": "^1.2.9", "typescript": "^5"}}