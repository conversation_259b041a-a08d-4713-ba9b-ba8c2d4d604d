# Document System Specification

## Architecture Overview

### Component Responsibilities

**API Routes** (File Operations)
- Upload/Download file handling
- Streaming and progress tracking
- Session validation and RLS enforcement
- Storage bucket operations

**Server Actions** (Business Logic)
- Document metadata operations
- Search and listing
- Polymorphic entity attachments
- Authorization and validation

**Storage System**
- Organization-scoped folder structure
- RLS policies for access control
- Automatic lifecycle management

## API Routes

### Upload Endpoint
```typescript
POST /api/documents/upload
Content-Type: multipart/form-data

Body:
- file: File
- entityType: 'case_file' | 'contact' | 'request' | 'appointment'
- entityId: string
- documentName?: string

Response:
- documentId: string
- filePath: string
- fileSize: number
```

### Download Endpoint
```typescript
GET /api/documents/[id]/download

Response:
- File stream with appropriate headers
- Content-Disposition: attachment
- Content-Type: based on file type
```

## Server Actions (Polymorphic Interface)

```typescript
// Entity-scoped operations
getEntityDocuments(entityType: string, entityId: string): DocumentMetadata[]
deleteDocument(documentId: string): boolean
updateDocumentMetadata(documentId: string, metadata: object): boolean
searchDocuments(query: string, filters: object): DocumentMetadata[]
getDocumentInfo(documentId: string): DocumentMetadata

// Integration pattern for any entity
<DocumentList entityType="case_file" entityId={caseFileId} />
<DocumentUpload entityType="contact" entityId={contactId} />
```

## Folder Structure

```
/documents/
  /{organization_id}/
    /case_files/
      /{case_file_id}/
        /{document_id}.{extension}
    /contacts/
      /{contact_id}/
        /{document_id}.{extension}
    /requests/
      /{request_id}/
        /{document_id}.{extension}
    /appointments/
      /{appointment_id}/
        /{document_id}.{extension}
    /templates/
      /{template_id}.{extension}
```

## Storage RLS Policies

### Bucket Policy (Organization Access Only)
```sql
CREATE POLICY "org_documents_access" ON storage.objects
FOR ALL USING (
  bucket_id = 'documents' AND
  (storage.foldername(name))[1] IN (
    SELECT organization_id::text
    FROM user_roles
    WHERE user_id = auth.uid()
  )
);
```

**Note:** Entity-level access control is handled at the application layer through Server Actions and API routes. This keeps the storage policies simple and maintainable while preserving the polymorphic pattern.

## Database RLS Policies

### Document Attachments Access (Organization Level)
```sql
CREATE POLICY "document_attachments_org_access" ON document_attachments
FOR ALL USING (
  organization_id IN (
    SELECT organization_id FROM user_roles WHERE user_id = auth.uid()
  )
);
```

**Access Control Strategy:**
- **Storage Level**: Organization segregation only
- **Database Level**: Organization segregation only
- **Application Level**: Entity-specific access validation in Server Actions and API routes

## Organization Lifecycle

### 1. Organization Creation (Simple Approach)
When a new organization is created in the wizard:

```typescript
// In organization creation wizard (final step)
async function completeOrganizationCreation(orgData: OrganizationData) {
  // 1. Create organization record
  const org = await createOrgRecord(orgData)

  // 2. Create storage folder structure (simple function call)
  await createOrgFolderStructure(org.id)

  // 3. RLS policies apply automatically

  return org
}
```

### 2. Folder Structure Creation
```typescript
async function createOrgFolderStructure(organizationId: string) {
  const folders = [
    `${organizationId}/case_files/.keep`,
    `${organizationId}/contacts/.keep`,
    `${organizationId}/requests/.keep`,
    `${organizationId}/appointments/.keep`,
    `${organizationId}/templates/.keep`
  ]

  // Create placeholder files to establish folder structure
  for (const folder of folders) {
    await supabase.storage
      .from('documents')
      .upload(folder, new Blob(['']))
  }
}
```

**Note:** No workflow system needed - simple function calls in the organization wizard are sufficient for this infrastructure setup.

## Implementation Steps

### Phase 1: Core Infrastructure
1. Create storage bucket with RLS policies
2. Implement API routes for upload/download
3. Create organization lifecycle hooks

### Phase 2: Server Actions
1. Implement polymorphic server actions
2. Create reusable UI components
3. Add search and metadata operations

### Phase 3: Integration
1. Integrate with case files domain
2. Add to contacts and requests
3. Implement document templates

### Phase 4: Advanced Features
1. Document versioning (if needed)
2. Bulk operations
3. Advanced search and filtering

## Security Model

- **Organization Isolation**: Enforced at storage and database level via RLS policies
- **Entity Access**: Validated at application level in Server Actions and API routes
- **Session-Based**: All operations validate current user session via cookies
- **No URL Sharing**: Downloads require active authentication (no signed URLs)
- **Audit Trail**: All operations logged in document_attachments table
- **Layered Security**: Storage RLS + Database RLS + Application validation
