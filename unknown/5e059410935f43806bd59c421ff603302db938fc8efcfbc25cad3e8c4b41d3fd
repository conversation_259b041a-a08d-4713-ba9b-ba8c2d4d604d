# Document Attachments Setup Guide

## Overview

The document attachment feature provides secure file upload, storage, and download capabilities for the application. This guide explains how to configure and use the document attachment system.

## Configuration Requirements

### 1. Supabase Storage Bucket Setup

The document attachment system requires a Supabase storage bucket to be configured. Follow these steps:

#### Step 1: Create Storage Bucket
1. Go to your Supabase project dashboard
2. Navigate to **Storage** in the left sidebar
3. Click **Create a new bucket**
4. Use the following configuration:
   - **Bucket name**: `document-attachments`
   - **Public bucket**: `false` (for security)
   - **File size limit**: `50MB` (recommended)
   - **Allowed MIME types**: Configure based on your needs

#### Step 2: Set Bucket Policies
Create the following RLS (Row Level Security) policies for the bucket:

```sql
-- Allow authenticated users to upload files
CREATE POLICY "Allow authenticated uploads" ON storage.objects
FOR INSERT TO authenticated
WITH CHECK (bucket_id = 'document-attachments');

-- Allow users to view their organization's files
CREATE POLICY "Allow organization file access" ON storage.objects
FOR SELECT TO authenticated
USING (
  bucket_id = 'document-attachments' AND
  (storage.foldername(name))[1] = (
    SELECT organization_id::text 
    FROM auth.users 
    WHERE auth.users.id = auth.uid()
  )
);

-- Allow users to delete their organization's files
CREATE POLICY "Allow organization file deletion" ON storage.objects
FOR DELETE TO authenticated
USING (
  bucket_id = 'document-attachments' AND
  (storage.foldername(name))[1] = (
    SELECT organization_id::text 
    FROM auth.users 
    WHERE auth.users.id = auth.uid()
  )
);
```

#### Step 3: Environment Configuration
Ensure your environment variables are properly set:

```env
# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key

# Storage Configuration
SUPABASE_STORAGE_BUCKET=document-attachments
```

### 2. Database Setup

The document attachment system uses the following database tables:

- `document_attachments`: Main table for attachment metadata
- `document_templates`: For document templates (optional)
- `document_signatures`: For electronic signatures (optional)

These tables are created by the migration: `20250524090831_create_document_domain.sql`

## Usage Guide

### 1. Basic File Upload

```tsx
import { uploadAttachment } from "@/app/actions/document/attachments";

// In your component
const handleUpload = async (files: File[]) => {
  const formData = new FormData();
  
  // Add files
  files.forEach(file => formData.append("files", file));
  
  // Add metadata
  formData.append("attached_to_type", "contact");
  formData.append("attached_to_id", contactId);
  formData.append("category", "identification");
  formData.append("description", "Contact identification documents");
  
  const result = await uploadAttachment(formData);
  
  if (result.success) {
    console.log("Upload successful:", result.data);
  } else {
    console.error("Upload failed:", result.error);
  }
};
```

### 2. List Attachments

```tsx
import { getAttachments, getEntityAttachments } from "@/app/actions/document/attachments";

// Get all attachments with pagination
const allAttachments = await getAttachments({
  page: 1,
  limit: 12,
  sortBy: "uploaded_at",
  sortOrder: "desc"
});

// Get attachments for specific entity
const contactAttachments = await getEntityAttachments("contact", contactId);
```

### 3. Download Files

```tsx
import { generateDownloadUrl } from "@/app/actions/document/downloads";

const handleDownload = async (attachmentId: string) => {
  const result = await generateDownloadUrl(attachmentId, 3600); // 1 hour expiry
  
  if (result.success) {
    // Open download URL
    window.open(result.data, '_blank');
  } else {
    console.error("Download failed:", result.error);
  }
};
```

### 4. Using the Document Attachment Component

```tsx
import { DocumentAttachment } from "@/components/ui/document-attachment";

function ContactDocuments({ contactId }: { contactId: string }) {
  const [documents, setDocuments] = useState([]);

  const handleUpload = async (files: File[]) => {
    // Upload logic here
  };

  const handleDelete = async (documentId: string) => {
    // Delete logic here
  };

  const handleDownload = (document: Document) => {
    // Download logic here
  };

  return (
    <DocumentAttachment
      title="Contact Documents"
      description="Upload and manage documents for this contact"
      documents={documents}
      onUpload={handleUpload}
      onDelete={handleDelete}
      onDownload={handleDownload}
      allowUpload={true}
      allowDelete={true}
    />
  );
}
```

## Security Features

### 1. Permission-Based Access Control

All document actions are protected by permissions:

- `document:attachment:view` - View attachments
- `document:attachment:create` - Upload attachments
- `document:attachment:update` - Update attachment metadata
- `document:attachment:delete` - Delete attachments
- `document:attachment:download` - Download attachments

### 2. File Validation

The system includes comprehensive file validation:

- File type validation (configurable MIME types)
- File size limits (configurable)
- Malicious file detection
- Content validation

### 3. Secure Storage

- Files are stored in private Supabase storage buckets
- Access is controlled through signed URLs with expiration
- Organization-level isolation
- Audit logging for all operations

## Troubleshooting

### Common Issues

1. **"404 bucket not found" Error**
   - Ensure the storage bucket is created in Supabase
   - Verify the bucket name matches the configuration
   - Check that RLS policies are properly set

2. **Upload Failures**
   - Check file size limits
   - Verify MIME type restrictions
   - Ensure user has proper permissions
   - Check network connectivity

3. **Download Issues**
   - Verify signed URL generation
   - Check file existence in storage
   - Ensure user has download permissions

### Debug Mode

Enable debug logging by setting:

```env
LOG_LEVEL=verbose
```

This will provide detailed logs for troubleshooting.

## API Reference

### Server Actions

#### Attachment Actions
- `getAttachments(params)` - List attachments with pagination
- `getEntityAttachments(entityType, entityId)` - Get attachments for entity
- `getAttachmentById(id)` - Get single attachment
- `uploadAttachment(formData)` - Upload new attachments
- `deleteAttachment(id)` - Delete attachment
- `updateAttachmentMetadata(id, formData)` - Update metadata

#### Download Actions
- `generateDownloadUrl(id, expiresIn)` - Generate secure download URL
- `trackDownload(attachmentId, metadata)` - Track download activity
- `validateDownloadAccess(id)` - Validate download permissions
- `getDownloadStats(id)` - Get download statistics

### Services

#### DocumentAttachmentService
Core service for attachment operations.

#### FileValidationService
Service for file validation and security checks.

## Support

For additional support or questions about the document attachment system:

1. Check the troubleshooting section above
2. Review the server logs for detailed error information
3. Ensure all configuration requirements are met
4. Contact @mouimet-infinisoft for configuration assistance

## Future Enhancements

Planned features for future releases:

1. **Bulk Download**: ZIP multiple files for download
2. **Document Templates**: Generate documents from templates
3. **Electronic Signatures**: Digital signature workflow
4. **Advanced Search**: Full-text search within documents
5. **Version Control**: Document versioning and history
6. **OCR Integration**: Text extraction from images and PDFs
